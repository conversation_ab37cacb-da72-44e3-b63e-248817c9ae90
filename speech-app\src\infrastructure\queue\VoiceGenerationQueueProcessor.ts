import { PrismaClient, TokenType, VoiceGenerationStatus, type VoiceGenerationQueue } from "@prisma/client";
import { ee } from "~/utils/pubsub"; // Import the pub/sub emitter
import { GoogleTTSGenerationService } from "~/infrastructure/tts/GoogleTTSGenerationService";
import { type ITTSGenerationService, type TTSGenerationConfig } from "~/domain/tts/ITTSGenerationService";
import * as path from 'node:path';
import wav from 'wav';
import { createQueuedFunction } from "~/utils/createQueuedFunction";
const MAX_CONCURRENT_TASKS = 5; // System-wide concurrency limit
const MAX_CONCURRENT_TASKS_PER_USER = 1; // Per-user concurrency limit
const PROCESS_QUEUE_TIMEOUT = 150000;

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function saveWaveFile(
   filename: string,
   pcmData: Buffer,
   channels = 1,
   rate = 24000,
   sampleWidth = 2,
) {
   return new Promise((resolve, reject) => {
      const writer = new wav.FileWriter(filename, {
            channels,
            sampleRate: rate,
            bitDepth: sampleWidth * 8,
      });

      writer.on('finish', resolve);
      writer.on('error', reject);

      writer.write(pcmData);
      writer.end();
   });
}
export class VoiceGenerationQueueProcessor {
  private ttsService: ITTSGenerationService;

  constructor(private prisma: PrismaClient, ttsService?: ITTSGenerationService) {
    this.ttsService = ttsService || new GoogleTTSGenerationService();
    this.promoteWaitingTaskForUser = createQueuedFunction(this.promoteWaitingTaskForUser.bind(this));
  }

  async addTask(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {
    const totalUserPendingTasks = await this.prisma.voiceGenerationQueue.count({
      where: {
        userId: userId,
        status: {
          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING],
        },
      },
    });
    const result =  await this.prisma.voiceGenerationQueue.create({
      data: {
        sentenceId: sentenceId,
        selectedBySentence: { connect: { id: sentenceId } },
        userId: userId,
        status: totalUserPendingTasks < MAX_CONCURRENT_TASKS_PER_USER ? VoiceGenerationStatus.PENDING : VoiceGenerationStatus.WAITING_USER,
      },
    });
    return result;
  }

  async processTask(task: VoiceGenerationQueue): Promise<void> {
   // Update task status to PROCESSING
      await this.prisma.voiceGenerationQueue.update({
        where: { id: task.id },
        data: {
          status: VoiceGenerationStatus.PROCESSING,
          startedAt: new Date(),
        },
      });

      // Send real-time update to frontend (status: PROCESSING)
      ee.emit('queueTaskUpdate', task);

      try {

       
        const sentence = await this.prisma.sentence.findUnique({
          where: { id: task.sentenceId },
        });

        if (!sentence) {
          throw new Error(`Sentence with ID ${task.sentenceId} not found.`);
        }

        const tokenCount = await this.ttsService.calTokenCount(sentence.text)
        
        const userAvailableTokenForTokenType = await this.prisma.userTokenAvailable.findUnique({
          where: { 
            userId_tokenType: {
              userId: task.userId,
              tokenType: TokenType.GOOGLE_GENERATIVE_AI,
            },
          },
        });

        if (!userAvailableTokenForTokenType || userAvailableTokenForTokenType.amount < tokenCount) {
          throw new Error(`Not enough tokens available. Required ${tokenCount}, but only ${userAvailableTokenForTokenType?.amount} available.`);
        }

        console.log(`Queue processor: Generating voice for sentence ${task.sentenceId} with text: "${sentence.text}"`);
        // TODO: Make voiceName configurable, perhaps from task metadata or user preferences
        const voiceName = 'Kore'; // Example Vietnamese voice name
        const { buffer:audioBuffer, totalTokenCount, tokenType, model } = await this.ttsService.generateVoice(sentence.text, sentence.generateConfig as TTSGenerationConfig);
        const audioFileName = `${task.id}.wav`;
        const audioDir = path.join(process.cwd(), 'public', 'generated-audio');
        const audioFilePath = path.join(audioDir, audioFileName);
        await saveWaveFile(audioFilePath, audioBuffer)
    
        const audioUrl = `/generated-audio/${audioFileName}`;
        const audioDuration = 5; // Placeholder duration, ideally calculated from audioBuffer or metadata

        const {completedTask, updatedUserTokenAvailable} = await this.prisma.$transaction(async tx => {
          // Upon success: Update task status to PROCESSED_SUCCESS
          const completedTask = await tx.voiceGenerationQueue.update({
            where: { id: task.id },
            data: {
              status: VoiceGenerationStatus.COMPLETED, // this update would release available capacity for the user
              completedAt: new Date(),
              audioUrl: audioUrl,
              audioDuration: audioDuration,
              totalTokenCount,
              model,
              tokenType,
              generateConfig: sentence.generateConfig || undefined,
            },
          });

          // Send real-time update to frontend (status: PROCESSED_SUCCESS, with results)
          // const completedTask = await this.prisma.voiceGenerationQueue.findUniqueOrThrow({ where: { id: task.id } }); // Fetch updated task with results
          console.log(`Queue processor: Task ${task.id} processed successfully.`);
          await tx.userTokenLedger.create({
              data: {
                userId: task.userId,
                tokenType: tokenType as TokenType,
                amount: -totalTokenCount,
                message: `Voice generation for sentence ${sentence.text}`,
                model
              },
          });
          const updatedUserTokenAvailable = await tx.userTokenAvailable.update({
            where: { 
              userId_tokenType: {
                userId: task.userId,
                tokenType: tokenType as TokenType,
              },
            },
            data: {
              amount: {
                decrement: totalTokenCount,
              },
            },
          });
          return {completedTask, updatedUserTokenAvailable};
        })
        ee.emit('userTokenAvailableUpdate', updatedUserTokenAvailable);
        ee.emit('queueTaskUpdate', completedTask);
        // Check for and promote waiting tasks for this user
        await this.promoteWaitingTaskForUser(task.userId);

      } catch (error) {
        // Upon failure: Update task status to FAILED
        console.error(`Queue processor: Task ${task.id} failed:`, error);
        await this.prisma.voiceGenerationQueue.update({
          where: { id: task.id },
          data: {
            status: VoiceGenerationStatus.FAILED,
            completedAt: new Date(),
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        });
        // Send real-time update to frontend (status: FAILED, with error)
        const failedTask = await this.prisma.voiceGenerationQueue.findUniqueOrThrow({ where: { id: task.id } }); // Fetch updated task with error
        ee.emit('queueTaskUpdate', failedTask);
        // Check for and promote waiting tasks for this user even on failure
        await this.promoteWaitingTaskForUser(task.userId);
      }
  }


  async processQueue(): Promise<void> {
    // Check current processing tasks
    const processingTasksCount = await this.prisma.voiceGenerationQueue.count({
      where: {
        status: VoiceGenerationStatus.PROCESSING,
      },
    });

    const availableCapacity = MAX_CONCURRENT_TASKS - processingTasksCount;

    if (availableCapacity <= 0) {
      console.log("Queue processor: Max concurrency reached. Waiting for tasks to complete.");
      return;
    }

    // Find pending tasks up to available capacity
    const pendingTasks = await this.prisma.voiceGenerationQueue.findMany({
      where: {
        status: VoiceGenerationStatus.PENDING,
      },
      orderBy: {
        createdAt: 'asc',
      },
      take: availableCapacity,
    });

    if (pendingTasks.length === 0) {
      console.log("Queue processor: No pending tasks.");
      return;
    }

    console.log(`Queue processor: Found ${pendingTasks.length} pending tasks. Processing...`);

    // Process tasks
    for (const task of pendingTasks) {
      this.processTask(task);
    }
    
    const timeoutTasks = await this.prisma.voiceGenerationQueue.findMany({
      where: {
        status: VoiceGenerationStatus.PROCESSING,
        startedAt: {
          lte: new Date(Date.now() - PROCESS_QUEUE_TIMEOUT),
        },
      },
    });

    if (timeoutTasks.length > 0) {
      console.log(`Queue processor: Found ${timeoutTasks.length} timed out tasks. Marking as failed.`);
      for (const task of timeoutTasks) {
        const failedTask = await this.prisma.voiceGenerationQueue.update({
          where: { id: task.id },
          data: {
            status: VoiceGenerationStatus.FAILED,
            completedAt: new Date(),
            error: 'Task timed out',
          },
        });
        // Send real-time update to frontend (status: FAILED, with error)
        ee.emit('queueTaskUpdate', failedTask);
        this.promoteWaitingTaskForUser(task.userId);
      }
    }


  }

  private async promoteWaitingTaskForUser(userId: string): Promise<void> {
    const waitingTask = await this.prisma.voiceGenerationQueue.findFirst({
      where: {
        userId: userId,
        status: VoiceGenerationStatus.WAITING_USER,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    if (waitingTask) {
      console.log(`Queue processor: Promoting waiting task ${waitingTask.id} for user ${userId}`);
      await this.prisma.voiceGenerationQueue.update({
        where: { id: waitingTask.id },
        data: {
          status: VoiceGenerationStatus.PENDING,
        },
      });
      // Trigger the processor again to pick up the newly pending task
      // TODO: Implement actual trigger mechanism - This will depend on how the processor is run (e.g., message queue, cron job, direct call)
      console.log("Queue processor: Triggering processor after promoting task.");
      return this.processQueue(); // Or send a message queue event
    }
  }
}

// Example usage (this would typically be run as a separate process or scheduled job)
// const prisma = new PrismaClient();
// const processor = new VoiceGenerationQueueProcessor(prisma);
// processor.processQueue().catch(console.error);