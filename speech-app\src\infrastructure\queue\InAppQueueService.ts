import type { PrismaClient, VoiceGenerationQueue } from "@prisma/client";
import { VoiceGenerationQueueProcessor } from "./VoiceGenerationQueueProcessor";
import { db } from "../persistence/prisma/client";
import { createQueuedFunction } from "../../utils/createQueuedFunction";

export class InAppQueueService {
  private processingPromise: Promise<void> | null = null;
  private queuedAddTask: (sentenceId: string, userId: string) => Promise<VoiceGenerationQueue>;

  constructor(private prisma: PrismaClient, private processor: VoiceGenerationQueueProcessor) {
    // Bind the processor's addTask method to its instance before queuing it
    const boundAddTask = this.processor.addTask.bind(this.processor);

    // Decorate the processor's addTask method with the queuing logic
    this.queuedAddTask = createQueuedFunction(boundAddTask);
  }

  async start(): Promise<void> {
    await this.processQueue();
  }

  async processQueue(): Promise<void> {
    if (this.processingPromise) {
      // If already processing, wait for the current process to finish
      return this.processingPromise;
    }

    // Start processing and store the promise
    this.processingPromise = this.processor.processQueue().finally(() => {
      // Reset the promise after processing is done (success or failure)
      this.processingPromise = null;
    });

    // Return the promise so callers can await the processing completion
    return this.processingPromise;
  }
  
  async addTask(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {
    return this.queuedAddTask(sentenceId, userId);
  }
}

// export const queueProcessor = new InAppQueueService(db, new VoiceGenerationQueueProcessor(db));