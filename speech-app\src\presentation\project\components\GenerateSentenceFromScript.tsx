"use client";

import React, { useState } from "react";
import Button from "../../@shared/components/Button";
import { api } from "../../../infrastructure/trpc/react";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";

interface GenerateSentenceFromScriptProps {
  projectId: string;
}

export const GenerateSentenceFromScript: React.FC<GenerateSentenceFromScriptProps> = ({
  projectId,
}) => {
  const [script, setScript] = useState("");
  const router = useRouter();
  const queryClient = useQueryClient();

  const createSentence = api.sentence.createSentence.useMutation({
    onSuccess: () => {},
  });

  const handleGenerate = async () => {
    if (!script.trim()) return;

    // Simple sentence splitting logic (can be improved for more complex cases)
    const sentences = script
      .split(/(?<=[.!?])\s+/)
      .filter((s) => s.trim() !== "");

    for (const sentenceText of sentences) {
      await createSentence.mutateAsync({
        projectId,
        text: sentenceText.trim(),
      });
    }
    queryClient.invalidateQueries({ queryKey: [['project', 'getById'], { input: { projectId:  projectId! }, type: 'query'}] });
    setScript(""); // Clear the textarea after generation
  };

  return (
    <div className="p-4 border rounded-md shadow-sm bg-white">
      <h3 className="text-lg font-semibold mb-4">Generate Sentences from Script</h3>
      <textarea
        className="w-full p-2 border rounded-md mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
        rows={6}
        placeholder="Paste your script here..."
        value={script}
        onChange={(e) => setScript(e.target.value)}
      ></textarea>
      <Button onClick={handleGenerate} disabled={createSentence.isPending}>
        {createSentence.isPending ? "Generating..." : "Generate Sentences"}
      </Button>
      {createSentence.isError && (
        <p className="text-red-500 mt-2">Error generating sentences.</p>
      )}
    </div>
  );
};