import type { IUserRepository } from '../../../domain/user/repositories/IUserRepository';
import { User } from '../../../domain/user/entities/User';
import { Email } from '../../../domain/user/value-objects/Email';
import type { CreateUserDto } from '../dtos/CreateUserDto';
import type { UserResponseDto } from '../dtos/UserResponseDto';

export class CreateUser {
  constructor(private userRepository: IUserRepository) {}

  public async execute(input: CreateUserDto): Promise<UserResponseDto> {
    const email = Email.create(input.email); // Value object creation
    const existingUser = await this.userRepository.findByEmail(email);

    if (existingUser) {
      throw new Error('User with this email already exists.');
    }

    const user = User.create({
      name: input.name,
      email: email,
    });

    await this.userRepository.save(user);

    return {
      id: user.id,
      name: user.name,
      email: user.email.value,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}