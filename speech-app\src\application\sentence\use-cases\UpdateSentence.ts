
import type { ISentenceRepository } from "~/domain/sentence/ISentenceRepository";
import type { Sentence } from "~/domain/sentence/entities/Sentence";

interface UpdateSentenceInput {
  sentenceId: string;
  voiceName?: string;
  seed?: number;
  model?: string;
}

export class UpdateSentence {
  constructor(private sentenceRepository: ISentenceRepository) {}

  async execute(input: UpdateSentenceInput): Promise<Sentence> {
    const existingSentence = await this.sentenceRepository.findById(input.sentenceId);

    if (!existingSentence) {
      throw new Error('Sentence not found');
    }

    // Update the generateConfig properties
    existingSentence.generateConfig = {
      voiceName: input.voiceName ?? existingSentence.generateConfig?.voiceName,
      seed: input.seed ?? existingSentence.generateConfig?.seed,
      model: input.model ?? existingSentence.generateConfig?.model,
    };

    const updatedSentence = await this.sentenceRepository.update(existingSentence);

    return updatedSentence;
  }
}
