import React, { useState, useRef, useEffect, type ReactNode } from 'react';

interface ContextDropdownProps {
  children: ReactNode; // The content to be displayed inside the dropdown menu
  trigger: ReactNode; // The element that triggers the dropdown
}

export const ContextDropdown: React.FC<ContextDropdownProps> = ({
  children,
  trigger,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null); // Use a div to wrap the trigger for ref

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen((prev) => !prev);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative">
      <div ref={triggerRef} onClick={toggleMenu}>
        {trigger}
      </div>
      {isOpen && (
        <div
          ref={menuRef}
          className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-sm py-1 ring-1 ring-gray-200 focus:outline-none z-10"
        >
          {children}
        </div>
      )}
    </div>
  );
};