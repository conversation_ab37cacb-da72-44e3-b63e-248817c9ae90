#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/src/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/src/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/cjs/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/dist/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules/mkdirp/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/mkdirp@3.0.1/node_modules:/mnt/e/project/ideas-app/speech/speech-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mkdirp/dist/cjs/src/bin.js" "$@"
else
  exec node  "$basedir/../mkdirp/dist/cjs/src/bin.js" "$@"
fi
