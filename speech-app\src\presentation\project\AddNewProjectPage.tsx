"use client"
import React, { useState } from 'react';
import SpeechLayout from "./components/SpeechLayout";
import SpeechSidebar from "./components/SpeechSidebar";
import Button from "~/presentation/@shared/components/Button";
import Input from "~/presentation/@shared/components/Input";
import { api } from "~/infrastructure/trpc/react";
import { useRouter } from 'next/navigation';

const AddNewProjectPage: React.FC = () => {
  const [projectTitle, setProjectTitle] = useState("");
  const [projectScript, setProjectScript] = useState("");
  const router = useRouter();

  const createProjectMutation = api.project.create.useMutation({
    onSuccess: (data) => {
      setProjectTitle("");
      setProjectScript("");
      router.push(`/projects/${data.id}`);
    },
  });

  const handleProjectSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createProjectMutation.mutate({
      name: projectTitle,
      script: projectScript,
    });
  };

  return (
     <SpeechLayout sidebarContent={
        <SpeechSidebar/>
      }>
        <div className="flex flex-col h-full items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Create a New Project
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        Start by giving your project a title and an sript.
                    </p>
                </div>
                <form className="mt-8 space-y-6" onSubmit={handleProjectSubmit}>
                    <div className="-space-y-px">
                        <div>
                            <Input
                                id="projectTitle"
                                label="Project Title"
                                type="text"
                                placeholder="e.g., My Awesome Speech Project"
                                value={projectTitle}
                                onChange={(e) => setProjectTitle(e.target.value)}
                                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                            />
                        </div>
                        <div className="mt-4">
                            <Input
                                id="projectScript"
                                label="Script"
                                required
                                textarea
                                rows={3}
                                placeholder="Script for your speech"
                                value={projectScript}
                                onChange={(e) => setProjectScript(e.target.value)}
                                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                            />
                        </div>
                    </div>

                    <div>
                        <Button
                            type="submit"
                            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            disabled={createProjectMutation.isPending}
                        >
                            {createProjectMutation.isPending ? "Creating..." : "Create Project"}
                        </Button>
                    </div>
                    {createProjectMutation.isError && (
                        <p className="mt-2 text-center text-sm text-rose-600">
                            Error: {createProjectMutation.error?.message}
                        </p>
                    )}
                </form>
            </div>
        </div>
    </SpeechLayout>
  );
};

export default AddNewProjectPage;