import { PrismaClient, type VoiceGenerationQueue } from "@prisma/client";
import { type ISentenceRepository, type PrismaSentenceWithRelation } from "~/domain/sentence/ISentenceRepository";
import { Sentence } from "~/domain/sentence/entities/Sentence";
import { VoiceGeneration } from "~/domain/voiceGeneration/entities/VoiceGeneration";
import { v4 as uuidv4 } from 'uuid';
import { type Sentence as PrismaSentence } from "@prisma/client";


export class PrismaSentenceRepository implements ISentenceRepository {
  constructor(private prisma: PrismaClient) {}

  async create(text: string, projectId: string): Promise<Sentence> {
    const newSentence = await this.prisma.$transaction(async (prisma) => {
      const result = await prisma.sentence.aggregate({
        where: { projectId },
        _max: {
          order: true,
        },
      });
      const newOrder = (result._max.order ?? 0) + 1;

      return prisma.sentence.create({
        data: {
          id: uuidv4(),
          text: text,
          order: newOrder,
          status: 'Draft', // Default status
          projectId: projectId,
          generateConfig: { voiceName: 'Kore', seed: randomSeed(), model: 'gemini-2.5-flash-preview-tts' },
        },
        include: {
          voiceGenerationQueueTasks: true,
          selectedVoiceGeneration: true,
        },
      });
    });

    return this.mapPrismaSentenceToDomain(newSentence);
  }


  async findById(id: string): Promise<Sentence | null> {
    const sentence = await this.prisma.sentence.findUnique({
      where: { id },
      include: {
        voiceGenerationQueueTasks: true,
        selectedVoiceGeneration: true,
      },
    });

    if (!sentence) {
      return null;
    }

    return this.mapPrismaSentenceToDomain(sentence);
  }

  async save(sentence: Sentence): Promise<Sentence> {
    const savedSentence = await this.prisma.sentence.upsert({
      where: { id: sentence.id },
      update: {
        text: sentence.text,
        order: sentence.order,
        tone: sentence.tone,
        emotion: sentence.emotion,
        audioUrl: sentence.audioUrl,
        audioDuration: sentence.audioDuration,
        status: sentence.status,
        projectId: sentence.projectId,
        selectedVoiceGenerationId: sentence.selectedVoiceGenerationId,
        updatedAt: new Date(),
        generateConfig: sentence.generateConfig as any, // Update generateConfig
      },
      create: {
        id: sentence.id,
        text: sentence.text,
        order: sentence.order,
        tone: sentence.tone,
        emotion: sentence.emotion,
        audioUrl: sentence.audioUrl,
        audioDuration: sentence.audioDuration,
        status: sentence.status,
        projectId: sentence.projectId,
        selectedVoiceGenerationId: sentence.selectedVoiceGenerationId,
        generateConfig: sentence.generateConfig as any, // Create generateConfig
      },
      include: {
        voiceGenerationQueueTasks: true,
        selectedVoiceGeneration: true,
      },
    });

    return this.mapPrismaSentenceToDomain(savedSentence);
  }

  async update(sentence: Sentence): Promise<Sentence> {
    return this.save(sentence);
  }
 
  async delete(id: string): Promise<void> {
    await this.prisma.sentence.delete({
      where: { id },
    });
  }
 
  async findHighestOrder(projectId: string): Promise<number> {
    const result = await this.prisma.sentence.aggregate({
      where: { projectId },
      _max: {
        order: true,
      },
    });
    // If no sentences exist, _max.order will be null, so return 0
    return result._max.order ?? 0;
  }
 
  mapPrismaSentenceToDomain(sentence: PrismaSentenceWithRelation): Sentence {
    return new Sentence(
      sentence.id,
      sentence.text,
      sentence.order,
      sentence.status,
      sentence.projectId,
      sentence.createdAt,
      sentence.updatedAt,
      sentence.tone || undefined,
      sentence.emotion || undefined,
      sentence.audioUrl || undefined,
      sentence.audioDuration || undefined,
      sentence.voiceGenerationQueueTasks?.map(
        (vg: any) =>
          new VoiceGeneration(
            vg.id,
            vg.audioUrl,
            vg.audioDuration || undefined,
            vg.status,
            vg.createdAt,
            vg.sentenceId,
            vg.error,
            vg.userId,
            vg.startedAt,
            vg.completedAt,
            vg.totalTokenCount,
            vg.tokenType,
            vg.model,
            vg.updatedAt || undefined,
            vg.generateConfig as any, // Pass generateConfig to VoiceGeneration constructor
          ),
      ),
      sentence.selectedVoiceGenerationId || undefined,
      sentence.selectedVoiceGeneration
        ? new VoiceGeneration(
            sentence.selectedVoiceGeneration.id,
            sentence.selectedVoiceGeneration.audioUrl,
            sentence.selectedVoiceGeneration.audioDuration || undefined,
            sentence.selectedVoiceGeneration.status,
            sentence.selectedVoiceGeneration.createdAt,
            sentence.selectedVoiceGeneration.sentenceId,
            sentence.selectedVoiceGeneration.error,
            sentence.selectedVoiceGeneration.userId,
            sentence.selectedVoiceGeneration.startedAt,
            sentence.selectedVoiceGeneration.completedAt,
            sentence.selectedVoiceGeneration.totalTokenCount,
            sentence.selectedVoiceGeneration.tokenType,
            sentence.selectedVoiceGeneration.model,
            sentence.selectedVoiceGeneration.updatedAt || undefined,
            sentence.selectedVoiceGeneration.generateConfig as any, // Pass generateConfig to VoiceGeneration constructor
          )
        : undefined,
      sentence.generateConfig as any, // Pass generateConfig to Sentence constructor
    );
  }
 
}
function randomSeed(): number {
  return Math.floor(Math.random() * 10000000);
}

