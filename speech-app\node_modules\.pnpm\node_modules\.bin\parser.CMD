@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\parser\bin\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\parser\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\parser\bin\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\parser\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules\@babel\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\@babel+parser@7.27.4\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@babel\parser\bin\babel-parser.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@babel\parser\bin\babel-parser.js" %*
)
