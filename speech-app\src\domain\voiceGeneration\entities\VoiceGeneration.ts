import { BaseEntity } from "~/domain/shared/BaseEntity";

export class VoiceGeneration extends BaseEntity {
  audioUrl: string | null;
  audioDuration?: number | null;
  sentenceId: string;
  status: string;
  error: string | null;
  userId: string;
  startedAt: Date | null;
  completedAt: Date | null;

  totalTokenCount: number;
  tokenType: string | null;
  model: string | null;

  generateConfig: {
    voiceName: string;
    seed: number;
    model: string;
  };

  constructor(
    id: string,
    audioUrl: string | null,
    audioDuration: number | undefined,
    status: string,
    createdAt: Date,
    sentenceId: string,
    error: string | null,
    userId: string,
    startedAt: Date | null,
    completedAt: Date | null,
    totalTokenCount: number,
    tokenType: string | null,
    model: string | null,
    updatedAt?: Date, // Make updatedAt optional as it might not be present on creation
    generateConfig: { voiceName: string; seed: number; model: string; } = { voiceName: '', seed: 0, model: '' },
  ) {
    super(id, createdAt, updatedAt ?? new Date()); // Pass updatedAt to super, default to new Date() if undefined
    this.audioUrl = audioUrl;
    this.audioDuration = audioDuration;
    this.sentenceId = sentenceId;
    this.status = status;
    this.error = error;
    this.userId = userId;
    this.startedAt = startedAt;
    this.totalTokenCount = totalTokenCount;
    this.tokenType = tokenType;
    this.model = model;
    this.completedAt = completedAt;
    this.generateConfig = generateConfig;
  }

  toResponseObject() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      audioUrl: this.audioUrl,
      audioDuration: this.audioDuration,
      sentenceId: this.sentenceId,
      status: this.status,
      error: this.error,
      userId: this.userId,
      startedAt: this.startedAt,
      completedAt: this.completedAt,
      totalTokenCount: this.totalTokenCount,
      tokenType: this.tokenType,
      model: this.model,
      generateConfig: this.generateConfig,
    };
  }
}