import { BaseEntity } from '../../shared/BaseEntity';
import { Email } from '../value-objects/Email';

interface UserProps {
  name: string;
  email: Email;
  createdAt?: Date;
  updatedAt?: Date;
}

export class User extends BaseEntity {
  private props: UserProps;

  private constructor(props: UserProps, id: string, createdAt: Date, updatedAt: Date) {
    super(id, createdAt, updatedAt);
    this.props = props;
  }

  public static create(props: UserProps, id?: string, createdAt?: Date, updatedAt?: Date): User {
    const newId = id ?? Math.random().toString(36).substring(2, 15); // Simple ID generation for now
    const newCreatedAt = createdAt ?? new Date();
    const newUpdatedAt = updatedAt ?? new Date();
    return new User(props, newId, newCreatedAt, newUpdatedAt);
  }

  get name(): string {
    return this.props.name;
  }

  get email(): Email {
    return this.props.email;
  }


  public updateName(name: string): void {
    this.props.name = name;
    this.updatedAt = new Date(); // Update BaseEntity's updatedAt
  }

  public updateEmail(email: Email): void {
    this.props.email = email;
    this.updatedAt = new Date(); // Update BaseEntity's updatedAt
  }
}