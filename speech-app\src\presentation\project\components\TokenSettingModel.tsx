import React from 'react';
import { api } from '../../../infrastructure/trpc/react';
import IconButton from '../../@shared/components/IconButton';
import { AiOutlineClose } from 'react-icons/ai';
import Button from '~/presentation/@shared/components/Button';

interface TokenSettingModelProps {
  isOpen: boolean;
  onClose: () => void;
}

const TokenSettingModel: React.FC<TokenSettingModelProps> = ({ isOpen, onClose }) => {
  const { data, isLoading, isError, error, refetch } = api.user.getUserTokens.useQuery();

  const { mutate, isPending: isMutating } = api.user.createTrialToken.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleGetTrialToken = () => {
    mutate();
  };

  const { mutate: topUpMutate, isPending: isTopUpMutating } = api.user.topUpTokens.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleTopUpTokens = () => {
    topUpMutate({ amount: 1000, tokenType: "GOOGLE_GENERATIVE_AI" });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600/20 overflow-y-auto h-full w-full z-50 flex justify-center items-center">
      <div className="relative p-5 w-96 shadow-sm rounded-xl bg-white">
        <div className="mt-3">
          <div className="flex justify-between items-center">
            <h3 className="text-lg leading-6 font-medium text-gray-800">Token Settings</h3>
            <IconButton icon={AiOutlineClose} onClick={onClose} />
          </div>
          <div className="mt-2 px-7 py-3">
            {isLoading && <p className="text-sm text-gray-500">Loading tokens...</p>}
            {isError && <p className="text-sm text-red-500">Error loading tokens: {error.message}</p>}
            {data && data.length > 0 ? (
              <React.Fragment>
                <ul className="list-disc list-inside text-sm text-gray-700">
                  {data.map((token) => (
                    <li key={token.id}>
                      {token.tokenType}: {token.amount}
                    </li>
                  ))}
                </ul>
                <div className="flex flex-col items-center mt-4">
                  <Button
                    size='small'
                    onClick={handleTopUpTokens}
                    // className="px-4 py-2 bg-sky-500 text-white text-base font-medium rounded shadow-sm hover:bg-sky-600 focus:outline-none focus:ring-2 focus:ring-sky-400/30"
                    disabled={isTopUpMutating}
                  >
                    {isTopUpMutating ? 'Topping Up...' : 'Top Up 1000 Tokens'}
                  </Button>
                </div>
              </React.Fragment>
            ) : (
              !isLoading && !isError && (
                <div className="flex flex-col items-center">
                  <p className="text-sm text-gray-500">No tokens available.</p>
                  <button
                    onClick={handleGetTrialToken}
                    className="mt-4 px-4 py-2 bg-sky-500 text-white text-base font-medium rounded shadow-sm hover:bg-sky-600 focus:outline-none focus:ring-2 focus:ring-sky-400/30"
                    disabled={isMutating}
                  >
                    {isMutating ? 'Getting Token...' : 'Get Trial Token'}
                  </button>
                  <button
                    onClick={handleTopUpTokens}
                    className="mt-4 px-4 py-2 bg-sky-500 text-white text-base font-medium rounded shadow-sm hover:bg-sky-600 focus:outline-none focus:ring-2 focus:ring-sky-400/30"
                    disabled={isTopUpMutating}
                  >
                    {isTopUpMutating ? 'Topping Up...' : 'Top Up 1000 Tokens'}
                  </button>
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenSettingModel;