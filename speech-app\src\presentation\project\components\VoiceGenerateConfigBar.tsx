import React from 'react';
import Input from '~/presentation/@shared/components/Input';

interface VoiceGenerateConfig {
  voiceName?: string;
  seed?: number;
  model?: string;
}

interface VoiceGenerateConfigBarProps {
  value: VoiceGenerateConfig | null;
  onChange: (value: VoiceGenerateConfig) => void;
  className?: string;
}
// Zephyr -- <PERSON>	Puck -- Upbeat	Charon -- Informative
// Kore -- Firm	Fenrir -- Excitable	Leda -- Youthful
// Orus -- Firm	Aoede -- Breezy	<PERSON>hoe -- Easy-going
// Autonoe -- Bright	Enceladus -- Breathy	Iapetus -- Clear
// Umbriel -- Easy-going	Algieba -- Smooth	Despina -- Smooth
// Erinome -- Clear	Algenib -- Gravelly	Rasalgethi -- Informative
// Laomedeia -- Upbeat	Achernar -- Soft	Alnilam -- Firm
// Schedar -- Even	Gacrux -- Mature	Pulcherrima -- Forward
// Achird -- Friendly	Zubenelgenubi -- Casual	Vindemiatrix -- Gentle
// Sadachbia -- Lively	Sadaltager -- Knowledgeable	Sulafat -- Warm
const voiceNameOptions = [
  { label: 'Zephyr (Bright)', value: 'Zephyr' },
  { label: 'Puck (Upbeat)', value: 'Puck' },
  { label: 'Charon (Informative)', value: 'Charon' },
  { label: 'Kore (Firm)', value: 'Kore' },
  { label: 'Fenrir (Excitable)', value: 'Fenrir' },
  { label: 'Leda (Youthful)', value: 'Leda' },
  { label: 'Orus (Firm)', value: 'Orus' },
  { label: 'Aoede (Breezy)', value: 'Aoede' },
  { label: 'Callirrhoe (Easy-going)', value: 'Callirrhoe' },
  { label: 'Autonoe (Bright)', value: 'Autonoe' },
  { label: 'Enceladus (Breathy)', value: 'Enceladus' },
  { label: 'Iapetus (Clear)', value: 'Iapetus' },
  { label: 'Umbriel (Easy-going)', value: 'Umbriel' },
  { label: 'Algieba (Smooth)', value: 'Algieba' },
  { label: 'Despina (Smooth)', value: 'Despina' },
  { label: 'Erinome (Clear)', value: 'Erinome' },
  { label: 'Algenib (Gravelly)', value: 'Algenib' },
  { label: 'Rasalgethi (Informative)', value: 'Rasalgethi' },
];

const modelOptions = [
  { label: 'Gemini 2.5 Flash Preview TTS', value: 'gemini-2.5-flash-preview-tts' },
  { label: 'Gemini 2.5 Pro Preview TTS', value: 'gemini-2.5-pro-preview-tts' }
];

const VoiceGenerateConfigBar: React.FC<VoiceGenerateConfigBarProps> = ({ value, onChange, className }) => {
  const handleVoiceNameChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange({ ...(value || {}), voiceName: e.target.value });
  };

  const handleSeedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...value, seed: Number(e.target.value) });
  };

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange({ ...value, model: e.target.value });
  };

  return (
    <div className={`flex items-center space-x-2 p-1 bg-gray-50 rounded-md text-sm ${className}`}>
      <span className="text-gray-600">Voice:</span>
      <select
        id="voiceName"
        value={value?.voiceName || ''}
        onChange={handleVoiceNameChange}
        className="block w-auto px-2 py-1 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
      >
        <option value="" disabled>Select Voice</option>
        {voiceNameOptions.map(option => (
          <option key={option.value} value={option.value}>{option.label}</option>
        ))}
      </select>
      
      <span className="text-gray-600">Model:</span>
      <select
        id="model"
        value={value?.model || ''}
        onChange={handleModelChange}
        className="block w-auto px-2 py-1 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
      >
        <option value="" disabled>Select Model</option>
        {modelOptions.map(option => (
          <option key={option.value} value={option.value}>{option.label}</option>
        ))}
      </select>

       <span className="text-gray-600">Seed:</span>
      <Input
        type="number"
        id="seed"
        value={value?.seed ?? ''}
        placeholder="Random"
        onChange={handleSeedChange}
        className="block w-24 px-2 py-1 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm"
      />
    </div>
  );
};

export default VoiceGenerateConfigBar;