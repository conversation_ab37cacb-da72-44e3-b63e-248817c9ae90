import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '../trpc';
import { ee } from '~/utils/pubsub'; // Import the event emitter

export const sentenceRouter = createTRPCRouter({
  createSentence: protectedProcedure
    .input(z.object({ projectId: z.string(), text: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const newSentence = await ctx.createSentenceUseCase.execute(input);
      return newSentence;
    }),

  deleteSentence: protectedProcedure
    .input(z.object({ sentenceId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Fetch the sentence before deleting to get the projectId
      const sentenceToDelete = await ctx.sentenceRepository.findById(input.sentenceId);
      if (sentenceToDelete) {
        await ctx.sentenceRepository.delete(input.sentenceId);
        ee.emit('sentenceUpdate', { projectId: sentenceToDelete.projectId, sentence: { id: input.sentenceId }, type: 'delete' }); // Emit event
      }
      return { id: input.sentenceId }; // Return deleted ID or success indicator
    }),

  updateSentence: protectedProcedure
    .input(z.object({
      sentenceId: z.string(),
      voiceName: z.string().optional(),
      seed: z.number().optional(),
      model: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const updatedSentence = await ctx.updateSentenceUseCase.execute(input);
      ee.emit('sentenceUpdate', { projectId: updatedSentence.projectId, sentence: updatedSentence, type: 'update' });
      return updatedSentence;
    }),
});