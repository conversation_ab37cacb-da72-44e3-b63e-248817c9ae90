{"name": "speech-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "test": "vitest", "migrate-deploy": "prisma migrate deploy"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@google/genai": "^1.3.0", "@prisma/client": "^6.5.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "wav": "^1.0.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "^20.17.57", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/wav": "^1.0.4", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.11.0"}