// import { describe, it, expect, vi, beforeEach } from 'vitest';
// import { GoogleTTSGenerationService } from '../GoogleTTSGenerationService';
// import { GoogleGenAI } from '@google/genai';

// // Mock the GoogleGenAI module
// // vi.mock('@google/genai', () => {
// //   const mockGenerateContentStream = vi.fn();
// //   const mockGoogleGenAI = vi.fn(() => ({
// //     models: {
// //       generateContentStream: mockGenerateContentStream,
// //     },
// //   }));
// //   return { GoogleGenAI: mockGoogleGenAI };
// // });

// describe('GoogleTTSGenerationService', () => {
//   let service: GoogleTTSGenerationService;
//   let mockGenerateContentStream: ReturnType<typeof vi.fn>;

//   beforeEach(() => {
//     // Clear all mocks before each test
//     vi.clearAllMocks();
//     // Ensure GOOGLE_GENERATIVE_AI_API_KEY is set for constructor
//     // process.env.GOOGLE_GENERATIVE_AI_API_KEY = 'test-api-key';
//     service = new GoogleTTSGenerationService();
//     // mockGenerateContentStream = (GoogleGenAI as any).mock.instances[0].models.generateContentStream;
//   });

//   it('should call generateContentStream with correct parameters', async () => {
//     const mockResponse = (async function* () {
//       yield { text: 'chunk1' };
//       yield { text: 'chunk2' };
//     })();

//     // mockGenerateContentStream.mockResolvedValue(mockResponse);

//     const text = `
// Thêm thao trường hứng tên lửa Nga, tư lệnh lục quân Ukraine từ chức
// Tư lệnh lục quân Ukraine thông báo sẽ từ chức, sau khi có thêm thao trường huấn luyện bị Nga tập kích tên lửa khiến nhiều binh sĩ thương vong.

// "Tôi đã quyết định nộp đơn từ chức, rời vị trí tư lệnh lục quân thuộc lực lượng vũ trang Ukraine. Quyết định xuất phát từ suy nghĩ rằng tôi phải chịu trách nhiệm cho thảm kịch tại Thao trường số 239, khiến những người lính của chúng ta thiệt mạng", tướng Mykhailo Drapatyi cho biết trong bài đăng trên mạng xã hội hôm 1/6.

// Drapatyi nhấn mạnh ông đã không hoàn thành nhiệm vụ của mình với tư cách là chỉ huy, giải thích rằng hành động của binh sĩ rất quan trọng nhưng trách nhiệm chính luôn thuộc về giới lãnh đạo. "Chính các chỉ huy là người đặt ra quy tắc, quyết định và phải chịu trách nhiệm về hậu quả", ông cho hay.

// Truyền hình quân đội Nga trước đó công bố video cho thấy máy bay không người lái (UAV) theo dõi thao trường tại vùng Novomoskovsk thuộc tỉnh Dnipro, cách tiền tuyến khoảng 135 km, rồi chỉ điểm cho tên lửa tập kích. Một tên lửa đạn đạo Iskander-M mang đầu đạn chùm kích nổ trên thao trường, gây ra đám cháy lớn và nổ thứ cấp.

// Cuộc tập kích tên lửa vào thao trường Ukraine trong video công bố hôm 1/6. Video: Zvezda

// "Đòn tấn công nhằm vào điểm tập trung của Lữ đoàn Cơ giới Độc lập số 33 và 158 của quân đội Ukraine", quân đội Nga cho hay. Truyền thông Nga thêm rằng cuộc tấn công khiến 75 binh sĩ Ukraine thương vong và làm cháy một kho đạn tại khu vực.

// Quân đội Ukraine thừa nhận một tên lửa Nga đã đánh trúng thao trường huấn luyện của lục quân vào sáng 1/6, khiến 12 binh sĩ đã thiệt mạng và hơn 60 người bị thương, nhưng từ chối tiết lộ địa điểm hứng tên lửa.

// Giới chức Ukraine khẳng định các binh sĩ không tập trung đông người và phần lớn ở trong hầm trú ẩn khi cuộc tấn công xảy ra. "Các quan chức sẽ phải chịu trách nhiệm nghiêm khắc nếu cuộc điều tra xác định thương vong xảy ra do hành động hoặc sự thiếu trách nhiệm của họ", quân đội Ukraine cho hay.

// Hãng tin AFP cho biết đây là một trong những lần hiếm hoi quân đội Ukraine thừa nhận thương vong do đòn tập kích của Nga.

// "Đó đều là những chàng trai trẻ thuộc biên chế một tiểu đoàn huấn luyện. Tôi xin gửi chia buồn sâu sắc nhất tới gia đình các nạn nhân và tất cả những người đã bị ảnh hưởng", tướng Drapatyi cho hay.

// Tướng Drapatyi, 42 tuổi, nhập ngũ từ năm 2004 và từng chỉ huy tại nhiều mặt trận trong chiến sự, trước khi trở thành tư lệnh lục quân Ukraine tháng 11/2024.
//     `;
//     const config = {
//       model: 'gemini-2.5-flash-preview-tts',
//       voiceName: 'Kore',
//       seed: 123,
//     };

//     const result = await service.generateVoiceStream(text, config);

//     // expect(mockGenerateContentStream).toHaveBeenCalledTimes(1);
//     // expect(mockGenerateContentStream).toHaveBeenCalledWith({
//     //   model: config.model,
//     //   contents: [{ parts: [{ text }] }],
//     //   config: {
//     //     responseModalities: ['AUDIO'],
//     //     seed: config.seed,
//     //     speechConfig: {
//     //       voiceConfig: {
//     //         prebuiltVoiceConfig: {
//     //           voiceName: config.voiceName,
//     //         },
//     //       },
//     //       languageCode: 'vi-VN',
//     //     },
//     //   },
//     // });

//     // Verify the stream content
//     const chunks = [];
//     for await (const chunk of result) {
//       chunks.push(chunk);
//       console.log(chunk);
//     }
//     expect(chunks).toEqual([{ text: 'chunk1' }, { text: 'chunk2' }]);
//   });

//   // it('should use default model and voiceName if not provided in config', async () => {
//   //   const mockResponse = (async function* () {
//   //     yield { text: 'chunk1' };
//   //   })();
//   //   mockGenerateContentStream.mockResolvedValue(mockResponse);

//   //   const text = 'Test default values';
//   //   const result = await service.generateVoiceStream(text);

//   //   expect(mockGenerateContentStream).toHaveBeenCalledTimes(1);
//   //   expect(mockGenerateContentStream).toHaveBeenCalledWith({
//   //     model: 'gemini-2.5-flash-preview-tts', // Default model
//   //     contents: [{ parts: [{ text }] }],
//   //     config: {
//   //       responseModalities: ['AUDIO'],
//   //       seed: undefined,
//   //       speechConfig: {
//   //         voiceConfig: {
//   //           prebuiltVoiceConfig: {
//   //             voiceName: 'Kore', // Default voiceName
//   //           },
//   //         },
//   //         languageCode: 'vi-VN',
//   //       },
//   //     },
//   //   });
//   //   const chunks = [];
//   //   for await (const chunk of result) {
//   //     chunks.push(chunk);
//   //   }
//   //   expect(chunks).toEqual([{ text: 'chunk1' }]);
//   // });

//   // it('should throw an error if GOOGLE_GENERATIVE_AI_API_KEY is not set', () => {
//   //   delete process.env.GOOGLE_GENERATIVE_AI_API_KEY;
//   //   expect(() => new GoogleTTSGenerationService()).toThrow('GOOGLE_GENERATIVE_AI_API_KEY is not set in environment variables.');
//   // });
// });