// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "sqlite"
    // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
    // Further reading:
    // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
    // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
    url      = env("DATABASE_URL")
}

model Post {
    id        Int      @id @default(autoincrement())
    name      String
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    createdBy   User   @relation(fields: [createdById], references: [id])
    createdById String

    @@index([name])
}

// Necessary for Next auth
model Account {
    id                       String  @id @default(cuid())
    userId                   String
    type                     String
    provider                 String
    providerAccountId        String
    refresh_token            String? // @db.Text
    access_token             String? // @db.Text
    expires_at               Int?
    token_type               String?
    scope                    String?
    id_token                 String? // @db.Text
    session_state            String?
    user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)
    refresh_token_expires_in Int?

    @@unique([provider, providerAccountId])
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
    id            String    @id @default(cuid())
    name          String
    email         String    @unique
    emailVerified DateTime?
    image         String?
    password      String?
    createdAt     DateTime  @default(now())
    updatedAt     DateTime  @updatedAt
    accounts      Account[]
    sessions      Session[]
    posts         Post[]
    projects      Project[]
    voiceGenerationQueueTasks VoiceGenerationQueue[] // Added relation
    UserTokenAvailable UserTokenAvailable[]
    UserTokenLedger UserTokenLedger[]
}

model VerificationToken {
    identifier String
    token      String   @unique
    expires    DateTime

    @@unique([identifier, token])
}

model Project {
    id          String     @id @default(cuid())
    name        String
    description String?
    createdAt   DateTime   @default(now())
    updatedAt   DateTime   @updatedAt
    userId      String
    user        User       @relation(fields: [userId], references: [id])
    sentences   Sentence[]

    @@index([name])
}

model Sentence {
    id           String    @id @default(cuid())
    text         String
    order        Int
    tone         String?
    emotion      String?
    seed         Int?
    voiceId      String? // Could reference a Voice model later if needed
    audioUrl     String? // URL to the generated audio file (deprecated, moved to VoiceGeneration)
    audioDuration Float?   // Duration of the generated audio in seconds (deprecated, moved to VoiceGeneration)
    status       String    @default("pending") // e.g., "pending", "generating", "completed", "failed"
    voiceGenerationQueueTasks VoiceGenerationQueue[] // Added relation
    selectedVoiceGenerationId String? @unique
    selectedVoiceGeneration VoiceGenerationQueue? @relation("SelectedVoiceGeneration", fields: [selectedVoiceGenerationId], references: [id])
    createdAt    DateTime  @default(now())
    updatedAt    DateTime  @updatedAt
    projectId    String
    project      Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
    generateConfig Json? // Added generateConfig field
    @@unique([projectId, order]) // Ensure unique order within a project
    @@index([projectId])
}

enum VoiceGenerationStatus {
  PENDING
  WAITING_USER
  PROCESSING
  COMPLETED
  FAILED
}

model VoiceGenerationQueue {
  id            String               @id @default(cuid())
  userId        String
  status        VoiceGenerationStatus @default(PENDING)
  createdAt     DateTime             @default(now())
  startedAt     DateTime?
  completedAt   DateTime?
  updatedAt    DateTime  @updatedAt
  error         String?
  
  sentenceId    String
  audioUrl      String?
  audioDuration Float?

  totalTokenCount Int @default(0)
  tokenType TokenType?
  model String?

  sentence Sentence @relation(fields: [sentenceId], references: [id], onDelete: Cascade)
  generateConfig Json? // Added generateConfig field
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  selectedBySentence Sentence? @relation("SelectedVoiceGeneration")

  @@index([userId, status])
  @@index([status, createdAt]) // Index for querying pending tasks
}

enum TokenType {
  GOOGLE_GENERATIVE_AI
}

model UserTokenAvailable {
  id        String   @id @default(cuid())
  userId    String
  tokenType  TokenType
  amount    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  @@unique([userId, tokenType])
}

model UserTokenLedger {
  id        String   @id @default(cuid())
  userId    String
  tokenType  TokenType
  model String?
  amount    Int
  message   String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
 