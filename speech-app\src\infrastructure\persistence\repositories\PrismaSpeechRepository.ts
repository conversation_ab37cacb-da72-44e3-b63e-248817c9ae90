import { PrismaClient } from '@prisma/client';
import type { ISpeechRepository } from '../../../domain/speech/repositories/ISpeechRepository';
import { Speech } from '../../../domain/speech/entities/Speech';

export class PrismaSpeechRepository implements ISpeechRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<Speech | null> {
    const speechData = await this.prisma.post.findUnique({ // Assuming 'Post' model is used for Speech
      where: { id: parseInt(id) }, // Assuming 'id' is an Int in Prisma for Post
    });
    if (!speechData) {
      return null;
    }
    return Speech.create(
      {
        userId: speechData.createdById,
        text: speechData.name, // Assuming 'name' field in Post is 'text' for Speech
        audioUrl: '', // No direct audioUrl in Post model, needs to be handled
        createdAt: speechData.createdAt,
        updatedAt: speechData.updatedAt,
      },
      speechData.id.toString(), // Convert Int id to string for BaseEntity
    );
  }

  async findByUserId(userId: string): Promise<Speech[]> {
    const speechDataList = await this.prisma.post.findMany({
      where: { createdById: userId },
    });

    return speechDataList.map((speechData) =>
      Speech.create(
        {
          userId: speechData.createdById,
          text: speechData.name,
          audioUrl: '', // No direct audioUrl in Post model, needs to be handled
          createdAt: speechData.createdAt,
          updatedAt: speechData.updatedAt,
        },
        speechData.id.toString(),
      ),
    );
  }

  async save(speech: Speech): Promise<void> {
    await this.prisma.post.upsert({
      where: { id: parseInt(speech.id) }, // Convert string id to Int for Prisma
      update: {
        name: speech.text,
        createdById: speech.userId,
        updatedAt: speech.updatedAt,
      },
      create: {
        name: speech.text,
        createdById: speech.userId,
        createdAt: speech.createdAt,
        updatedAt: speech.updatedAt,
      },
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.post.delete({
      where: { id: parseInt(id) },
    });
  }
}