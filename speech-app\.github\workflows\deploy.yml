name: Deploy Todoist to Server

on:
  push:
    branches:
      - main  # or your deployment branch

jobs:
  deploy:
    name: Deploy via SSH
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '23'

      - name: Install pnpm
        uses: pnpm/action-setup@v2 # Use the official pnpm setup action
        with:
          version: latest # Or pin to a specific pnpm version like '8'
          run_install: false # We'll run install in the next step
      # Add pnpm caching (optional but recommended)
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Build Next.js app
        run: npm run build

      - name: Generate ecosystem.config.cjs from template
        run: |
          export TARGET_DIR="${{ secrets.TARGET_DIR }}"
          export PORT=3000
          export GOOGLE_CLIENT_ID="${{ vars.GOOGLE_CLIENT_ID }}"
          export GOOGLE_CLIENT_SECRET="${{ secrets.GOOGLE_CLIENT_SECRET }}"
          export AUTH_SECRET="${{ secrets.AUTH_SECRET }}"
          export DATABASE_URL="${{ secrets.DATABASE_URL }}"
          export NEXTAUTH_URL="${{ secrets.NEXTAUTH_URL }}"
          export GOOGLE_GENERATIVE_AI_API_KEY="${{ secrets.GOOGLE_GENERATIVE_AI_API_KEY }}"
          envsubst < deploy/ecosystem.config.template > ecosystem.config.cjs
          echo "Generated ecosystem.config.cjs:"
          cat ecosystem.config.cjs # Log the generated file for verification

      - name: Copy files to server
        uses: appleboy/scp-action@v0.1.4
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: ".next,public,package.json,package-lock.json,pnpm-lock.yaml,prisma,ecosystem.config.cjs"
          target: "${{ secrets.TARGET_DIR }}"

      - name: SSH and restart PM2
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            PM2_PATH=/root/.local/share/pnpm/pm2
            
            cd ${{ secrets.TARGET_DIR }}
            #cpulimit -l 80 pnpm install
            pnpm install
             # Make the database URL available to the Prisma command environment
            echo "Exporting DATABASE_URL for Prisma migration..."
            export DATABASE_URL="${{ secrets.DATABASE_URL }}"
            # Verify it's set (optional debugging - be careful logging secrets)
            # echo "DATABASE_URL is now set in the shell."
            # --- Run Prisma Migration ---
            echo "Running Prisma migrations..."
            pnpm run migrate-deploy
            $PM2_PATH startOrReload ecosystem.config.cjs --env production
            $PM2_PATH save
            $PM2_PATH list
