"use client";

import React from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import Button from '../../@shared/components/Button';
import LinkButton from '../../@shared/components/LinkButton';
import IconButton from '../../@shared/components/IconButton';
import { ContextDropdown } from '../../@shared/components/ContextDropdown';
import TokenSettingModel from './TokenSettingModel';
import TokenUsage from './TokenUsage';

interface HeaderProps {
  onToggleSidebar?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar }) => {
  const { data: session, status } = useSession();
  const [isTokenModalOpen, setIsTokenModalOpen] = React.useState(false);

  const handleOpenTokenModal = () => {
    setIsTokenModalOpen(true);
  };

  const handleCloseTokenModal = () => {
    setIsTokenModalOpen(false);
  };

  const getInitials = (name?: string | null) => {
    if (!name) return '?';
    const names = name.split(' ');
    if (names.length === 1) return names[0]?.charAt(0).toUpperCase() ?? '?';
    return (names[0]?.charAt(0) ?? '') + (names[names.length - 1]?.charAt(0) ?? '').toUpperCase();
  };

  return (
    <header className="bg-white shadow-md py-3 px-6 sticky top-0 z-10"> {/* Added sticky and z-index */}
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-3">
          {onToggleSidebar && (
            <button
              onClick={onToggleSidebar}
              className="p-2 rounded-md text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 md:hidden"
              aria-label="Toggle sidebar"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}
          <Link href="/" className="text-2xl font-bold text-indigo-600 hover:text-indigo-700 transition-colors">
            SpeechApp
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <TokenUsage />
          {status === 'loading' && (
            <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
          )}

          {status === 'unauthenticated' && (
            <LinkButton href="/auth/login" variant="primary" size="medium">
              Login
            </LinkButton>
          )}

          {status === 'authenticated' && session?.user && (
            <>
            <ContextDropdown
              trigger={
                 <div className="flex items-center space-x-2">
                    <div className="w-10 h-10 rounded-full bg-indigo-500 text-white flex items-center justify-center font-semibold text-sm overflow-hidden">
                      {session.user.image ? (
                        <img src={session.user.image} alt={session.user.name ?? 'User Avatar'} className="w-full h-full object-cover" />
                      ) : (
                        getInitials(session.user.name)
                      )}
                    </div>
                    {/* <span className="text-gray-700 text-sm font-medium hidden sm:block">
                      {session.user.name ?? 'User'}
                    </span> */}
                </div>
              }
            >
              <button
                onClick={handleOpenTokenModal}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Tokens
              </button>
            </ContextDropdown>
            <TokenSettingModel isOpen={isTokenModalOpen} onClose={handleCloseTokenModal} />
             
              <Button
                variant="secondary"
                size="medium"
                onClick={() => signOut({ callbackUrl: '/auth/login' })}
              >
                Logout
              </Button>
            </>
          )}
        
        </div>
      </div>
    </header>
  );
};

export default Header;