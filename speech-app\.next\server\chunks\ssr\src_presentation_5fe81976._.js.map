{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/presentation/%40shared/components/Input.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: string;\r\n  id: string;\r\n  textarea?: boolean;\r\n  rows?: number;\r\n  disabled?: boolean;\r\n  loading?: boolean; // Added loading prop\r\n}\r\n\r\nconst Input: React.FC<InputProps> = ({ label, id, textarea, rows, className, disabled, loading, ...props }) => {\r\n  const baseClasses = \"w-full px-4 py-2 border rounded transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-400/30 focus:border-transparent\"; // Adjusted rounded, focus ring, and border on focus\r\n  const disabledClasses = \"bg-gray-100 cursor-not-allowed\";\r\n  const loadingClasses = \"opacity-75 animate-pulse\"; // Simple loading animation\r\n\r\n  const inputClasses = `${baseClasses} ${className || ''} ${disabled || loading ? disabledClasses : 'bg-gray-50 border-gray-300'} ${loading ? loadingClasses : ''}`; // Added bg-gray-50\r\n\r\n  return (\r\n    <div>\r\n      {label && (\r\n        <label htmlFor={id} className=\"block text-gray-700 text-sm font-medium mb-2\">\r\n          {label}\r\n        </label>\r\n      )}\r\n      {textarea ? (\r\n        <textarea\r\n          id={id}\r\n          rows={rows}\r\n          className={`${inputClasses} resize-y`}\r\n          disabled={disabled || loading} // Disable when loading\r\n          {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}\r\n        />\r\n      ) : (\r\n        <input\r\n          type=\"text\" // Default type\r\n          id={id}\r\n          className={inputClasses}\r\n          disabled={disabled || loading} // Disable when loading\r\n          {...props}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Input;"], "names": [], "mappings": ";;;;;AAWA,MAAM,QAA8B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;IACxG,MAAM,cAAc,sJAAsJ,oDAAoD;IAC9N,MAAM,kBAAkB;IACxB,MAAM,iBAAiB,4BAA4B,2BAA2B;IAE9E,MAAM,eAAe,GAAG,YAAY,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,YAAY,UAAU,kBAAkB,6BAA6B,CAAC,EAAE,UAAU,iBAAiB,IAAI,EAAE,mBAAmB;IAEtL,qBACE,6VAAC;;YACE,uBACC,6VAAC;gBAAM,SAAS;gBAAI,WAAU;0BAC3B;;;;;;YAGJ,yBACC,6VAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,GAAG,aAAa,SAAS,CAAC;gBACrC,UAAU,YAAY;gBACrB,GAAI,KAAK;;;;;qCAGZ,6VAAC;gBACC,MAAK,OAAO,eAAe;;gBAC3B,IAAI;gBACJ,WAAW;gBACX,UAAU,YAAY;gBACrB,GAAG,KAAK;;;;;;;;;;;;AAKnB;uCAEe", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/presentation/%40shared/components/Button.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary' | 'danger';\r\n  size?: 'small' | 'medium' | 'large';\r\n  loading?: boolean;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'medium',\r\n  loading = false,\r\n  disabled,\r\n  className,\r\n  ...props\r\n}) => {\r\n  const baseClasses = \"font-semibold rounded-lg shadow-md transition duration-300 ease-in-out transform\";\r\n  const disabledClasses = \"opacity-50 cursor-not-allowed\";\r\n  const loadingClasses = \"opacity-75 animate-pulse\";\r\n\r\n  const variantClasses = {\r\n    primary: \"bg-indigo-600 hover:bg-indigo-700 text-white\",\r\n    secondary: \"bg-gray-200 hover:bg-gray-300 text-gray-800\",\r\n    danger: \"bg-red-600 hover:bg-red-700 text-white\",\r\n  };\r\n\r\n  const sizeClasses = {\r\n    small: \"py-2 px-4 text-sm\",\r\n    medium: \"py-3 px-6 text-base\",\r\n    large: \"py-4 px-8 text-lg\",\r\n  };\r\n\r\n  const buttonClasses = `\r\n    ${className || ''}\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabled || loading ? disabledClasses : ''}\r\n    ${loading ? loadingClasses : ''}\r\n    \r\n  `.replace(/\\s+/g, ' ').trim(); // Clean up extra spaces\r\n\r\n  return (\r\n    <button\r\n      className={buttonClasses}\r\n      disabled={disabled || loading}\r\n      {...props}\r\n    >\r\n      {loading ? (\r\n        <span className=\"flex items-center justify-center\">\r\n          <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n          </svg>\r\n          Loading...\r\n        </span>\r\n      ) : (\r\n        children\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button;"], "names": [], "mappings": ";;;;;AASA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IACpB,MAAM,kBAAkB;IACxB,MAAM,iBAAiB;IAEvB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;IACrB,EAAE,aAAa,GAAG;IAClB,EAAE,YAAY;IACd,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;IACpB,EAAE,YAAY,UAAU,kBAAkB,GAAG;IAC7C,EAAE,UAAU,iBAAiB,GAAG;;EAElC,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,IAAI,wBAAwB;IAEvD,qBACE,6VAAC;QACC,WAAW;QACX,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,wBACC,6VAAC;YAAK,WAAU;;8BACd,6VAAC;oBAAI,WAAU;oBAA6C,OAAM;oBAA6B,MAAK;oBAAO,SAAQ;;sCACjH,6VAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,6VAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAC/C;;;;;;mBAIR;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/presentation/auth/LoginPage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { signIn } from \"next-auth/react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Input from \"~/presentation/@shared/components/Input\";\r\nimport Button from \"~/presentation/@shared/components/Button\";\r\n\r\nexport default function LoginPage() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const router = useRouter();\r\n\r\n  const handleEmailLogin = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setError(\"\");\r\n\r\n    const result = await signIn(\"credentials\", {\r\n      redirect: false,\r\n      email,\r\n      password,\r\n    });\r\n    debugger\r\n    if (!result?.error) {\r\n      router.push(\"/projects\");\r\n    } else {\r\n      setError(result?.error || \"Invalid credentials.\");\r\n    }\r\n  };\r\n\r\n  const handleGoogleSignIn = () => {\r\n    signIn(\"google\", { callbackUrl: \"/projects\" });\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"w-full max-w-md space-y-8 rounded-lg bg-white p-10 shadow-2xl\">\r\n        <div className=\"text-center\">\r\n          <svg\r\n            className=\"mx-auto h-12 w-12 text-indigo-600\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth=\"2\"\r\n              d=\"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z\"\r\n            ></path>\r\n          </svg>\r\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\r\n            Sign in to your SpeakVoice account\r\n          </h2>\r\n          <p className=\"mt-2 text-sm text-gray-600\">\r\n            Or <a href=\"/auth/register\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">create a new account</a>\r\n          </p>\r\n        </div>\r\n        <form className=\"mt-8 space-y-6\" onSubmit={handleEmailLogin}>\r\n          <div className=\"rounded-md shadow-sm -space-y-px\">\r\n            <div>\r\n              <Input\r\n                id=\"email-address\"\r\n                name=\"email\"\r\n                type=\"email\"\r\n                autoComplete=\"email\"\r\n                required\r\n                className=\"relative block w-full appearance-none rounded-t-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm\"\r\n                placeholder=\"Email address\"\r\n                value={email}\r\n                onChange={(e) => setEmail(e.target.value)}\r\n              />\r\n            </div>\r\n            <div>\r\n              <Input\r\n                id=\"password\"\r\n                name=\"password\"\r\n                type=\"password\"\r\n                autoComplete=\"current-password\"\r\n                required\r\n                className=\"relative block w-full appearance-none rounded-b-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm\"\r\n                placeholder=\"Password\"\r\n                value={password}\r\n                onChange={(e) => setPassword(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {error && <p className=\"text-center text-sm text-red-600\">{error}</p>}\r\n\r\n          <div>\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150 ease-in-out\"\r\n            >\r\n              Sign in with Email\r\n            </Button>\r\n          </div>\r\n        </form>\r\n\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 flex items-center\">\r\n            <div className=\"w-full border-t border-gray-300\" />\r\n          </div>\r\n          <div className=\"relative flex justify-center text-sm\">\r\n            <span className=\"bg-white px-2 text-gray-500\">Or continue with</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n          <Button\r\n            onClick={handleGoogleSignIn}\r\n            className=\"group relative flex w-full justify-center rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-900 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition duration-150 ease-in-out\"\r\n          >\r\n            <img src=\"https://www.svgrepo.com/show/355037/google.svg\" alt=\"Google logo\" className=\"h-5 w-5 mr-2\" />\r\n            Sign in with Google\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,SAAS;QAET,MAAM,SAAS,MAAM,CAAA,GAAA,iPAAA,CAAA,SAAM,AAAD,EAAE,eAAe;YACzC,UAAU;YACV;YACA;QACF;QACA,QAAQ;QACR,IAAI,CAAC,QAAQ,OAAO;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS,QAAQ,SAAS;QAC5B;IACF;IAEA,MAAM,qBAAqB;QACzB,CAAA,GAAA,iPAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YAAE,aAAa;QAAY;IAC9C;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;sCAEN,cAAA,6VAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAY;gCACZ,GAAE;;;;;;;;;;;sCAGN,6VAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6VAAC;4BAAE,WAAU;;gCAA6B;8CACrC,6VAAC;oCAAE,MAAK;oCAAiB,WAAU;8CAAoD;;;;;;;;;;;;;;;;;;8BAG9F,6VAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;8CACC,cAAA,6VAAC,uJAAA,CAAA,UAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8CAG5C,6VAAC;8CACC,cAAA,6VAAC,uJAAA,CAAA,UAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,cAAa;wCACb,QAAQ;wCACR,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;wBAKhD,uBAAS,6VAAC;4BAAE,WAAU;sCAAoC;;;;;;sCAE3D,6VAAC;sCACC,cAAA,6VAAC,wJAAA,CAAA,UAAM;gCACL,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;8BAIlD,6VAAC;8BACC,cAAA,6VAAC,wJAAA,CAAA,UAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,6VAAC;gCAAI,KAAI;gCAAiD,KAAI;gCAAc,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOnH", "debugId": null}}]}