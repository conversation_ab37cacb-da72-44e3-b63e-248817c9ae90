export abstract class BaseEntity {
  public readonly id: string;
  public readonly createdAt: Date;
  public updatedAt: Date;

  constructor(id: string, createdAt: Date, updatedAt: Date) {
    this.id = id;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public equals(entity?: BaseEntity): boolean {
    if (entity === null || entity === undefined) {
      return false;
    }
    if (this === entity) {
      return true;
    }
    if (!this.isEntity(entity)) {
      return false;
    }
    return this.id === entity.id;
  }

  private isEntity(v: unknown): v is BaseEntity {
    return v instanceof BaseEntity;
  }
}