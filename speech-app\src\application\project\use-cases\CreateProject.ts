import type { IProjectRepository } from "~/domain/project/IProjectRepository";
import { Project } from "~/domain/project/entities/Project";
import { v4 as uuidv4 } from 'uuid';
import type { ISentenceRepository } from "~/domain/sentence/ISentenceRepository";

interface CreateProjectRequest {
  name: string;
  description?: string | null;
  script?: string;
  userId: string;
}

export class CreateProject {
  constructor(private projectRepository: IProjectRepository, private sentenceRepository: ISentenceRepository) {}

  async execute(request: CreateProjectRequest): Promise<Project> {
    const project = Project.create(
      uuidv4(),
      new Date(),
      new Date(),
      {
        name: request.name,
        description: request.description,
        userId: request.userId,
      },
    );

    try {
      await this.projectRepository.save(project);
      if(request.script) {
        await this.sentenceRepository.create(request.script, project.id);
      }
      return project;
    } catch (error) {
      // Handle the error, e.g., log it, throw a custom application error, etc.
      console.error("Failed to save project:", error);
      throw new Error("Could not create project."); // Example error handling
    }
  }
}