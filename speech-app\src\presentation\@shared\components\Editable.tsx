import React, { useState, useRef, useEffect } from 'react';

interface EditableProps {
  initialValue: string;
  onSave: (newValue: string) => void;
  className?: string;
  textClassName?: string;
  inputClassName?: string;
  displayTag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'div' | 'p' | 'span';
}

const Editable: React.FC<EditableProps> = ({
  initialValue,
  onSave,
  className,
  textClassName,
  inputClassName,
  displayTag = 'h1', // Default to h1
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(initialValue);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    onSave(value);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      onSave(value);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setValue(initialValue); // Revert to initial value on escape
    }
  };

  const DisplayComponent = displayTag;

  return (
    <div className={className}>
      {isEditing ? (
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className={inputClassName}
        />
      ) : (
        React.createElement(DisplayComponent, { className: textClassName, onDoubleClick: handleDoubleClick }, value)
      )}
    </div>
  );
};

export default Editable;