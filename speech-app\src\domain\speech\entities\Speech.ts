import { BaseEntity } from '../../shared/BaseEntity';
import { v4 as uuidv4 } from 'uuid';
interface SpeechProps {
  userId: string;
  text: string;
  audioUrl: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Speech extends BaseEntity {
  userId: string;
  text: string;
  audioUrl: string;

  private constructor(
    id: string,
    userId: string,
    text: string,
    audioUrl: string,
    createdAt: Date,
    updatedAt: Date,
  ) {
    super(id, createdAt, updatedAt);
    this.userId = userId;
    this.text = text;
    this.audioUrl = audioUrl;
  }

  public static create(props: SpeechProps, id?: string, createdAt?: Date, updatedAt?: Date): Speech {
  
    const newCreatedAt = createdAt ?? new Date();
    const newUpdatedAt = updatedAt ?? new Date();
    return new  Speech(
      uuidv4(),
      props.userId,
      props.text,
      props.audioUrl,
      newCreatedAt,
      newUpdatedAt,
    );
  }

  public updateText(text: string): void {
    this.text = text;
    this.updatedAt = new Date();
  }

  public updateAudioUrl(audioUrl: string): void {
    this.audioUrl = audioUrl;
    this.updatedAt = new Date();
  }
}