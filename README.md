# Speak Voice Project

## Project Overview

The "Speak Voice Project" is a web-based application designed to empower users to transform text into high-quality spoken audio using advanced Text-to-Speech (TTS) AI. It caters to a diverse audience including content creators, students, and teachers, enabling them to easily create audio content ranging from short stories to full-length audiobooks.

## Key Features

*   **Sentence-Level AI Audio Generation:** Generate audio for text sentence by sentence, with options for fine-tuning.
*   **Granular Editing & Control:** Edit individual sentences, add new content anywhere, and reorder sections.
*   **AI Voice Customization:** Regenerate audio with different tones, emotions, and seed parameters for varied output.
*   **Real-time Playback:** Listen to the entire project at any point during the creation process.
*   **Responsive Design:** Accessible and functional across web browsers and mobile devices.

## Technical Stack

The application leverages the **Next.js T3 Stack** for both frontend and backend development, utilizing **Prisma** for database interactions. AI voice generation is handled by a scalable **Python worker** communicating via **gRPC** with the backend, ensuring efficient and robust audio synthesis. Generated audio files are stored in cloud storage for seamless access and playback.

## Project Phases

The development will proceed in phases, starting with a Core MVP (Minimum Viable Product) that establishes the foundational features, followed by enhancements and advanced functionalities.