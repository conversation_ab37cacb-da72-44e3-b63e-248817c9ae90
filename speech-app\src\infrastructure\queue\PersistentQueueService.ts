import type { PrismaClient, VoiceGenerationQueue, DeadLetterQueue } from "@prisma/client";
import { VoiceGenerationStatus, QueuePriority } from "@prisma/client";
import { randomUUID } from "crypto";
import { ee } from "~/utils/pubsub";

export interface QueueTask {
  id: string;
  userId: string;
  sentenceId: string;
  priority: QueuePriority;
  scheduledAt?: Date;
  maxAttempts: number;
  generateConfig?: any;
}

export interface QueueOptions {
  priority?: QueuePriority;
  delay?: number; // milliseconds
  maxAttempts?: number;
  scheduledAt?: Date;
}

export class PersistentQueueService {
  private readonly nodeId: string;
  private readonly lockTimeout = 5 * 60 * 1000; // 5 minutes
  private readonly maxConcurrentTasks = 5;
  private readonly defaultMaxAttempts = 3;

  constructor(private prisma: PrismaClient) {
    this.nodeId = `node-${randomUUID()}`;
  }

  /**
   * Add a task to the persistent queue
   */
  async enqueue(
    sentenceId: string, 
    userId: string, 
    options: QueueOptions = {}
  ): Promise<VoiceGenerationQueue> {
    const {
      priority = QueuePriority.NORMAL,
      delay = 0,
      maxAttempts = this.defaultMaxAttempts,
      scheduledAt
    } = options;

    const now = new Date();
    const taskScheduledAt = scheduledAt || (delay > 0 ? new Date(now.getTime() + delay) : now);
    
    // Check user's current pending/processing tasks for rate limiting
    const userActiveTasks = await this.prisma.voiceGenerationQueue.count({
      where: {
        userId,
        status: {
          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING, VoiceGenerationStatus.WAITING_USER]
        }
      }
    });

    const status = userActiveTasks >= 3 
      ? VoiceGenerationStatus.WAITING_USER 
      : (taskScheduledAt > now ? VoiceGenerationStatus.SCHEDULED : VoiceGenerationStatus.PENDING);

    const task = await this.prisma.voiceGenerationQueue.create({
      data: {
        userId,
        sentenceId,
        status,
        priority,
        scheduledAt: taskScheduledAt,
        maxAttempts,
        attempts: 0,
        selectedBySentence: { connect: { id: sentenceId } }
      }
    });

    ee.emit('queueTaskUpdate', task);
    return task;
  }

  /**
   * Dequeue and lock the next available task
   */
  async dequeue(): Promise<VoiceGenerationQueue | null> {
    const now = new Date();
    const lockExpiry = new Date(now.getTime() + this.lockTimeout);

    // Find and lock the next available task atomically
    const task = await this.prisma.$transaction(async (tx) => {
      // Find the next task to process
      const nextTask = await tx.voiceGenerationQueue.findFirst({
        where: {
          status: {
            in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.RETRYING]
          },
          OR: [
            { scheduledAt: null },
            { scheduledAt: { lte: now } }
          ],
          OR: [
            { lockExpiry: null },
            { lockExpiry: { lt: now } } // Lock has expired
          ]
        },
        orderBy: [
          { priority: 'desc' }, // Higher priority first
          { scheduledAt: 'asc' }, // Earlier scheduled tasks first
          { createdAt: 'asc' }    // FIFO for same priority
        ]
      });

      if (!nextTask) return null;

      // Lock the task
      return tx.voiceGenerationQueue.update({
        where: { id: nextTask.id },
        data: {
          status: VoiceGenerationStatus.PROCESSING,
          processingNode: this.nodeId,
          lockExpiry,
          startedAt: now,
          attempts: { increment: 1 }
        }
      });
    });

    if (task) {
      ee.emit('queueTaskUpdate', task);
    }

    return task;
  }

  /**
   * Mark task as completed
   */
  async markCompleted(
    taskId: string, 
    result: { audioUrl: string; audioDuration: number; totalTokenCount: number; tokenType?: string; model?: string }
  ): Promise<void> {
    const task = await this.prisma.voiceGenerationQueue.update({
      where: { id: taskId },
      data: {
        status: VoiceGenerationStatus.COMPLETED,
        completedAt: new Date(),
        audioUrl: result.audioUrl,
        audioDuration: result.audioDuration,
        totalTokenCount: result.totalTokenCount,
        tokenType: result.tokenType,
        model: result.model,
        processingNode: null,
        lockExpiry: null
      }
    });

    ee.emit('queueTaskUpdate', task);
    
    // Promote waiting tasks for this user
    await this.promoteWaitingTasksForUser(task.userId);
  }

  /**
   * Mark task as failed and handle retry logic
   */
  async markFailed(taskId: string, error: string): Promise<void> {
    const task = await this.prisma.voiceGenerationQueue.findUnique({
      where: { id: taskId }
    });

    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    if (task.attempts >= task.maxAttempts) {
      // Move to dead letter queue
      await this.moveToDeadLetterQueue(task, error);
    } else {
      // Schedule retry with exponential backoff
      const retryDelay = Math.pow(2, task.attempts) * 1000; // 2^attempts seconds
      const nextRetryAt = new Date(Date.now() + retryDelay);

      const updatedTask = await this.prisma.voiceGenerationQueue.update({
        where: { id: taskId },
        data: {
          status: VoiceGenerationStatus.RETRYING,
          error,
          nextRetryAt,
          processingNode: null,
          lockExpiry: null
        }
      });

      ee.emit('queueTaskUpdate', updatedTask);
    }

    // Promote waiting tasks for this user
    await this.promoteWaitingTasksForUser(task.userId);
  }

  /**
   * Move task to dead letter queue
   */
  private async moveToDeadLetterQueue(task: VoiceGenerationQueue, finalError: string): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      // Create dead letter queue entry
      await tx.deadLetterQueue.create({
        data: {
          originalTaskId: task.id,
          userId: task.userId,
          sentenceId: task.sentenceId,
          failureReason: finalError,
          attempts: task.attempts,
          lastError: task.error,
          originalPayload: {
            priority: task.priority,
            maxAttempts: task.maxAttempts,
            generateConfig: task.generateConfig,
            createdAt: task.createdAt
          }
        }
      });

      // Update original task
      const deadTask = await tx.voiceGenerationQueue.update({
        where: { id: task.id },
        data: {
          status: VoiceGenerationStatus.DEAD_LETTER,
          error: finalError,
          completedAt: new Date(),
          processingNode: null,
          lockExpiry: null
        }
      });

      ee.emit('queueTaskUpdate', deadTask);
    });
  }

  /**
   * Promote waiting tasks for a user when capacity becomes available
   */
  private async promoteWaitingTasksForUser(userId: string): Promise<void> {
    const userActiveTasks = await this.prisma.voiceGenerationQueue.count({
      where: {
        userId,
        status: {
          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING]
        }
      }
    });

    if (userActiveTasks < 3) {
      const waitingTask = await this.prisma.voiceGenerationQueue.findFirst({
        where: {
          userId,
          status: VoiceGenerationStatus.WAITING_USER
        },
        orderBy: { createdAt: 'asc' }
      });

      if (waitingTask) {
        const promotedTask = await this.prisma.voiceGenerationQueue.update({
          where: { id: waitingTask.id },
          data: { status: VoiceGenerationStatus.PENDING }
        });

        ee.emit('queueTaskUpdate', promotedTask);
      }
    }
  }

  /**
   * Process scheduled tasks (move SCHEDULED -> PENDING when time comes)
   */
  async processScheduledTasks(): Promise<number> {
    const now = new Date();
    
    const result = await this.prisma.voiceGenerationQueue.updateMany({
      where: {
        status: VoiceGenerationStatus.SCHEDULED,
        scheduledAt: { lte: now }
      },
      data: {
        status: VoiceGenerationStatus.PENDING,
        scheduledAt: null
      }
    });

    return result.count;
  }

  /**
   * Process retry tasks (move RETRYING -> PENDING when retry time comes)
   */
  async processRetryTasks(): Promise<number> {
    const now = new Date();
    
    const result = await this.prisma.voiceGenerationQueue.updateMany({
      where: {
        status: VoiceGenerationStatus.RETRYING,
        nextRetryAt: { lte: now }
      },
      data: {
        status: VoiceGenerationStatus.PENDING,
        nextRetryAt: null
      }
    });

    return result.count;
  }

  /**
   * Release expired locks (for crashed workers)
   */
  async releaseExpiredLocks(): Promise<number> {
    const now = new Date();
    
    const result = await this.prisma.voiceGenerationQueue.updateMany({
      where: {
        status: VoiceGenerationStatus.PROCESSING,
        lockExpiry: { lt: now }
      },
      data: {
        status: VoiceGenerationStatus.PENDING,
        processingNode: null,
        lockExpiry: null,
        startedAt: null
      }
    });

    return result.count;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const stats = await this.prisma.voiceGenerationQueue.groupBy({
      by: ['status'],
      _count: { status: true }
    });

    const deadLetterCount = await this.prisma.deadLetterQueue.count();

    return {
      byStatus: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      }, {} as Record<string, number>),
      deadLetterQueue: deadLetterCount,
      nodeId: this.nodeId
    };
  }

  /**
   * Get queue size
   */
  async size(): Promise<number> {
    return this.prisma.voiceGenerationQueue.count({
      where: {
        status: {
          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING, VoiceGenerationStatus.RETRYING]
        }
      }
    });
  }

  /**
   * Clear completed and failed tasks older than specified days
   */
  async cleanup(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.prisma.voiceGenerationQueue.deleteMany({
      where: {
        status: {
          in: [VoiceGenerationStatus.COMPLETED, VoiceGenerationStatus.FAILED, VoiceGenerationStatus.DEAD_LETTER]
        },
        completedAt: { lt: cutoffDate }
      }
    });

    return result.count;
  }
}
