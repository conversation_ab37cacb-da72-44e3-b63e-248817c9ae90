import React, { useState } from 'react';
import Header from '~/presentation/project/components/Header';

interface SpeechLayoutProps {
  sidebarContent: React.ReactNode;
  children: React.ReactNode;
}

const SpeechLayout: React.FC<SpeechLayoutProps> = ({ sidebarContent, children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  return (
    <div className="relative flex h-screen bg-gray-50 overflow-hidden">
      {/* Left Sidebar */}
      <aside
        className={`
          fixed inset-y-0 left-0 z-30
          w-72 md:w-1/4 bg-slate-100 p-6 border-r border-slate-200 overflow-y-auto
          transform transition-transform duration-300 ease-in-out
          ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          md:relative md:translate-x-0 md:flex-shrink-0
        `}
      >
        {sidebarContent}
      </aside>

      {/* Main Content */}
      {/* Overlay for small screens when sidebar is open */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/30 md:hidden"
          onClick={toggleSidebar}
          aria-hidden="true"
        ></div>
      )}

      <main className="flex-1 flex flex-col bg-white overflow-y-auto transition-all duration-300 ease-in-out">
        <Header onToggleSidebar={toggleSidebar} />
        <div className="flex-1 p-8 overflow-y-auto"> {/* Content area scrolls independently */}
          {children}
        </div>
      </main>
    </div>
  );
};

export default SpeechLayout;