# UI Design System (Condensed for AI)

## Principles
- Clarity: Clear hierarchy, intuitive layout.
- Simplicity: Minimalist, focused content.
- User-Centered: Comfort and usability first.
- Responsiveness: Mobile-first, fluid resizing.
- Consistency: Reusable patterns and behaviors.

## Colors
- Primary: sky-500, indigo-500
- Secondary: cyan-400, violet-400
- Neutral: gray-50, gray-100, gray-600, gray-800
- Status: rose-400 (error), emerald-400 (success)
- Notes: Soft tones, low contrast, accessible combinations.

## Typography
- Font: --font-geist-sans, Inter, sans-serif
- Headings: text-4xl → text-xl, semi-bold
- Body: text-base, relaxed line height
- Detail: text-sm, gray-500
- Style: Calm, whitespace-focused

## Layout
- Spacing: 8px scale (p-, m-, gap- utilities)
- Container: max-w-7xl
- Whitespace: Used for visual grouping
- Breakpoints: sm, md, lg (Tailwind convention)

## Components
- Card: bg-white, rounded-xl, shadow-sm
- Button: sky-500 or gray-200, rounded, hover/active states
- Input: bg-gray-50, gray placeholder, soft focus ring
- Toolbar: bg-gray-50, minimal content
- Tags: small, rounded, muted unless active

## Icons
- Style: Outline default, filled when active
- Size: w-4 h-4, flex-aligned
- Color: gray-600 or accents with opacity

## Imagery
- Style: Clean, soft gradients or pastel tones
- Avoid: Harsh contrast, saturated media

## Accessibility
- Contrast: Meet WCAG AA
- Focus: ring-2 ring-sky-400/30
- Keyboard: All controls tabbable
- Semantics: Use native HTML, ARIA when needed

## Interaction
- Hover: Shadow or color shift
- Active: Scale or darker shade
- Loading: Skeletons or soft spinner
- Transitions: ease-in-out, duration-150

## Tokens (suggested)
- Colors: --color-bg-muted, --color-text-soft
- Radius: --radius-card
- Shadows: --shadow-light
