# Detailed Breakdown: Core MVP Phase - Speak Voice Project

The goal of the Core MVP is to deliver a functional application that allows users to create basic voice projects, generate AI audio sentence-by-sentence, review and regenerate, and listen to the full project.

## 1. Initial Setup & Documentation

*   **Tasks:**
    *   Create the initial web project structure (Next.js T3 Stack).
    *   Establish technical documentation guidelines (e.g., code comments, READMEs for modules).
    *   Define design UI pattern guidelines (e.g., component library usage, styling conventions).
*   **Considerations:**
    *   Automate project setup where possible.
    *   Choose a clear and consistent documentation style.
    *   Select a UI component library and define its usage.

## 2. User Authentication & Basic Account Management

*   **Tasks:**
    *   Implement user registration (email/password).
    *   Implement user login/logout.
    *   Basic user profile (e.g., display name).
    *   Password reset functionality.
*   **Considerations:**
    *   Use NextAuth.js (part of T3 Stack) for authentication.
    *   Secure password hashing and storage.
    *   Basic input validation for forms.

## 3. Project Creation & Management (Basic)

*   **Tasks:**
    *   Allow users to create new projects with a title.
    *   Display a list of the user's projects.
    *   Ability to open/select a project for editing.
    *   Basic project saving (auto-save or manual save).
*   **Considerations:**
    *   Database schema design for `Project` entity (e.g., `id`, `userId`, `title`, `createdAt`, `updatedAt`).
    *   Frontend UI for project listing and creation.

## 4. Text Input & Sentence Segmentation

*   **Tasks:**
    *   Provide a text area for users to input their script/essay.
    *   Implement client-side logic to automatically segment the input text into sentences or manageable phrases.
    *   Display segmented sentences in an editable list.
*   **Considerations:**
    *   Robust sentence boundary detection (handling abbreviations, ellipses, etc.).
    *   Allow manual adjustment of sentence boundaries if auto-segmentation is imperfect.
    *   Frontend component for displaying and reordering sentences.

## 5. AI Voice Generation (Sentence-by-Sentence)

*   **Tasks:**
    *   Backend API endpoint to trigger AI voice generation for a single sentence.
    *   Frontend UI to initiate generation for an individual sentence.
    *   Python AI Worker:
        *   Receive text and parameters (e.g., voice ID, basic tone/emotion hints) via gRPC.
        *   Call a chosen TTS AI service (e.g., a single default voice from Google Cloud TTS, AWS Polly, or a similar service).
        *   Generate audio file (e.g., MP3).
        *   Store the generated audio file in cloud storage (e.g., AWS S3, Google Cloud Storage).
        *   Return the URL/path to the generated audio to the backend.
    *   Backend: Store the audio file URL/path associated with the sentence in the database.
*   **Considerations:**
    *   Initial choice of a single, reliable TTS AI provider for MVP.
    *   Error handling for AI generation failures.
    *   Asynchronous processing for AI generation to avoid blocking the UI.
    *   Efficient storage and retrieval of audio files.

## 6. Sentence Editing & Reordering

*   **Tasks:**
    *   Allow users to directly edit the text of an individual sentence.
    *   Implement drag-and-drop or similar UI for reordering sentences within a project.
    *   Ability to add a new blank sentence at any point in the project.
    *   Ability to delete a sentence.
*   **Considerations:**
    *   Frontend state management for sentence list.
    *   Database updates for text changes and order changes.
    *   Clear visual feedback for editing and reordering.

## 7. Review & Regenerate with Parameters

*   **Tasks:**
    *   For each generated sentence, display a playback control (e.g., play button).
    *   Provide UI controls (e.g., dropdowns, sliders) to select different tone, emotion, and seed parameters for regeneration.
    *   Trigger regeneration for a specific sentence with new parameters.
*   **Considerations:**
    *   Initial set of tone/emotion options will be limited based on the chosen TTS AI provider's capabilities.
    *   "Seed" parameter might be a simple integer input that influences the AI's output variation.
    *   Clear indication of which sentences have generated audio and which need regeneration.

## 8. Full Project Playback

*   **Tasks:**
    *   Implement a "Play All" button or similar control.
    *   Frontend logic to sequentially play all generated audio segments for the current project.
    *   Basic playback controls (play/pause, progress bar).
*   **Considerations:**
    *   Efficient streaming of audio files from cloud storage.
    *   Seamless transitions between sentence audio files.

## 9. Basic MP3 Export

*   **Tasks:**
    *   Backend endpoint to concatenate all generated audio files for a project into a single MP3.
    *   Frontend UI to trigger the export.
    *   Provide a download link for the generated MP3.
*   **Considerations:**
    *   Server-side audio concatenation (e.g., using `ffmpeg` or a dedicated audio processing library in Python).
    *   Handling potentially long processing times for large projects.
 
## 10. Responsive Web UI

*   **Tasks:**
    *   Design and implement a clean, intuitive user interface using Next.js components.
    *   Ensure the layout and functionality adapt well to different screen sizes (desktop, tablet, mobile).
*   **Considerations:**
    *   Use a UI component library (e.g., Tailwind CSS, Material UI, Chakra UI) for faster development.
    *   Focus on core user flows for MVP.

## 11. Deployment & Infrastructure (MVP Level)

*   **Tasks:**
    *   Set up a basic deployment pipeline for the Next.js application (e.g., Vercel, Netlify).
    *   Deploy the Python AI Worker (e.g., on a cloud VM, serverless function, or container service like Docker/Kubernetes).
    *   Configure database and cloud storage.
*   **Considerations:**
    *   Start with cost-effective cloud services for MVP.
    *   Basic monitoring for application and AI worker.