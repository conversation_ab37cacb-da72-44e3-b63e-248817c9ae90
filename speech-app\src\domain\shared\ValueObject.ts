
interface ValueObjectProps {
  [key: string]: unknown;
}

export abstract class ValueObject<T extends ValueObjectProps> {
  public readonly props: T;

  constructor(props: T) {
    this.props = Object.freeze(props);
  }

  public equals(vo?: ValueObject<T>): boolean {
    if (vo === null || vo === undefined) {
      return false;
    }
    if (this.props === vo.props) {
      return true;
    }
    return JSON.stringify(this.props) === JSON.stringify(vo.props);
  }
}