import { PrismaClient } from '@prisma/client';
import type { IUserRepository } from '../../../domain/user/repositories/IUserRepository';
import { User } from '../../../domain/user/entities/User';
import { Email } from '../../../domain/user/value-objects/Email';

export class PrismaUserRepository implements IUserRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: string): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { id },
    });
    if (!userData) {
      return null;
    }
    return User.create(
      {
        name: userData.name,
        email: Email.create(userData.email),
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      },
      userData.id,
    );
  }

  async findByEmail(email: Email): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { email: email.value },
    });
    if (!userData) {
      return null;
    }
    return User.create(
      {
        name: userData.name,
        email: Email.create(userData.email),
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      },
      userData.id,
    );
  }

  async save(user: User): Promise<void> {
    await this.prisma.user.upsert({
      where: { id: user.id },
      update: {
        name: user.name,
        email: user.email.value,
        updatedAt: user.updatedAt,
      },
      create: {
        id: user.id,
        name: user.name,
        email: user.email.value,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.user.delete({
      where: { id },
    });
  }
}