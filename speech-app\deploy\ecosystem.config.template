// E:/project/ideas-app/my-speech/ecosystem.config.template.js
// Note: Use placeholders like ${VARIABLE_NAME} which will be replaced by envsubst

module.exports = {
    apps : [{
            name   : 'speech-app', // The name for your app in PM2
            script : 'pnpm',
            args   : 'start',
            watch  : false, // Disable watching, rely on deployments for updates
            autorestart: true, // Automatically restart if the app crashes
            max_memory_restart: '1G', // Optional: Restart if it exceeds memory limit

            // --- Environment variables for production ---
            // --- These placeholders will be replaced by GitHub Secrets ---
            env_production: {
                NODE_ENV: 'production',
                PORT: ${PORT}, // Placeholder for PORT
                GOOGLE_CLIENT_ID: '${GOOGLE_CLIENT_ID}',
                GOOGLE_CLIENT_SECRET: '${GOOGLE_CLIENT_SECRET}',
                AUTH_SECRET: '${AUTH_SECRET}',
                DATABASE_URL: '${DATABASE_URL}',
                NEXTAUTH_URL: '${NEXTAUTH_URL}',
                AUTH_TRUST_HOST: '${NEXTAUTH_URL}',
                GOOGLE_GENERATIVE_AI_API_KEY: '${GOOGLE_GENERATIVE_AI_API_KEY}'
            }
    }]
};