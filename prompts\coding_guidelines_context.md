# LLM Coding Guidelines Context for `speech-app`

This document provides a condensed overview of the `speech-app` project's architecture and development guidelines, specifically tailored for LLM-assisted coding of small features.

## 1. Architectural Overview: DDD & Clean Architecture

The `speech-app` project follows **Domain-Driven Design (DDD)** principles and a **Clean Architecture** layered structure. This means:

*   **Domain Layer (Core)**: Contains core business logic, entities, value objects, and repository *interfaces*. It is framework-agnostic.
    *   Path: [`speech-app/src/domain`](speech-app/src/domain)
    *   Examples: [`User.ts`](speech-app/src/domain/user/entities/User.ts), [`Email.ts`](speech-app/src/domain/user/value-objects/Email.ts), [`IUserRepository.ts`](speech-app/src/domain/user/repositories/IUserRepository.ts)
*   **Application Layer**: Orchestrates domain logic for specific use cases. Contains application services (use cases) and Data Transfer Objects (DTOs). Depends only on the Domain layer.
    *   Path: [`speech-app/src/application`](speech-app/src/application)
    *   Examples: [`CreateSpeech.ts`](speech-app/src/application/speech/use-cases/CreateSpeech.ts), [`CreateUserDto.ts`](speech-app/src/application/user/dtos/CreateUserDto.ts)
*   **Infrastructure Layer**: Implements domain repository interfaces and handles external concerns (database, auth, tRPC server). Depends on Domain and Application layers.
    *   Path: [`speech-app/src/infrastructure`](speech-app/src/infrastructure)
    *   Examples: [`PrismaUserRepository.ts`](speech-app/src/infrastructure/persistence/repositories/PrismaUserRepository.ts), [`trpc/routers/user.ts`](speech-app/src/infrastructure/trpc/routers/user.ts)
*   **Presentation Layer**: User interface components (React) and Next.js API routes. Consumes Application/Infrastructure services.
    *   Path: [`speech-app/src/presentation`](speech-app/src/presentation), [`speech-app/src/app`](speech-app/src/app)
    *   Examples: [`ProjectPage.tsx`](speech-app/src/presentation/project/ProjectPage.tsx), [`api/trpc/[trpc]/route.ts`](speech-app/src/app/api/trpc/[trpc]/route.ts)

## 2. Development Workflow for New Features

When adding a small feature, follow an "inside-out" approach:

1.  **Domain**: Define/update entities, value objects, and repository interfaces.
2.  **Application**: Create/update use cases and DTOs that interact with the domain.
3.  **Infrastructure**: Implement concrete repositories (e.g., Prisma-based) and expose use cases via tRPC routers.
4.  **Presentation**: Develop UI components that interact with the tRPC API.

## 3. Key Considerations for LLM Coding

*   **File Placement**: Ensure new files are placed in the correct layer and domain-specific subdirectory (e.g., a new user-related DTO goes into `speech-app/src/application/user/dtos/`).
*   **Type Safety**: Always use TypeScript. Define clear interfaces and types.
*   **Code Style**: Adhere to existing ESLint and Prettier configurations.
*   **Dependencies**: Respect the architectural dependency rules (e.g., Domain should not import from Infrastructure).
*   **tRPC**: For API interactions, use tRPC. New API endpoints should be defined in `speech-app/src/infrastructure/trpc/routers/` and call application layer use cases.
*   **Prisma**: For database interactions, use Prisma. Repository implementations will reside in `speech-app/src/infrastructure/persistence/repositories/`.