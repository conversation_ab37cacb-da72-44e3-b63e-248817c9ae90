@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\src\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\cjs\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\dist\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules\mkdirp\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\mkdirp@3.0.1\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\mkdirp\dist\cjs\src\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\mkdirp\dist\cjs\src\bin.js" %*
)
