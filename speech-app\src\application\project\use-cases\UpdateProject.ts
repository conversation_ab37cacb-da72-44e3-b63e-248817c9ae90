import type { IProjectRepository } from "~/domain/project/IProjectRepository";
import type { Project } from "~/domain/project/entities/Project";

interface UpdateProjectParams {
  projectId: string;
  name?: string;
  description?: string;
}

export class UpdateProject {
  constructor(private projectRepository: IProjectRepository) {}

  async execute(params: UpdateProjectParams): Promise<Project> {
    const existingProject = await this.projectRepository.getById(params.projectId);

    if (!existingProject) {
      throw new Error("Project not found.");
    }

    // In a more robust domain model, Project might have methods like `rename(name: string)`
    if (params.name !== undefined) {
      existingProject.name = params.name;
    }
    
    if (params.description !== undefined) {
      existingProject.updateDescription(params.description);
    }

    await this.projectRepository.save(existingProject);
    return existingProject;
  }
}