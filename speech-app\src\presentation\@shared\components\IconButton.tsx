import React from 'react';
import type { ButtonHTMLAttributes } from 'react';
import { twMerge } from 'tailwind-merge';

interface IconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  size?: 'small' | 'medium' | 'large'; // Added size prop
  variant?: 'primary' | 'secondary' | 'danger'; // Added variant prop
  icon?: React.ElementType; // Added icon prop
  loading?: boolean; // Added loading prop
}

const IconButton: React.FC<IconButtonProps> = ({ className, size = 'medium', variant = 'primary', icon: Icon, loading = false, ...rest }) => {
  const baseStyles = 'p-1 rounded-full hover:bg-gray-200 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed';
  // Although size and variant are not directly used in baseStyles, they are now part of the component's interface and props.
  const mergedStyles = twMerge(baseStyles, className);

  return (
    <button className={mergedStyles} {...rest}>
      {loading ? '...' : Icon ? <Icon /> : null}
    </button>
  );
};

export default IconButton;