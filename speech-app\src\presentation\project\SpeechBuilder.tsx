"use client"
import IconButton from '../@shared/components/IconButton';
import { FaEllipsisH } from 'react-icons/fa';
import { BsSoundwave, BsTrash } from "react-icons/bs";
import React, { useState, useEffect } from 'react';
import Button from "~/presentation/@shared/components/Button";
import Input from "~/presentation/@shared/components/Input";
import Editable from '~/presentation/@shared/components/Editable';
import { api } from "~/infrastructure/trpc/react";
import { format } from "date-fns";
import SpeechLayout from '~/presentation/project/components/SpeechLayout';
import SpeechSidebar from './components/SpeechSidebar';
import { useQueryClient } from '@tanstack/react-query';
import type { Project } from '~/domain/project/entities/Project';
import type { Sentence } from '~/domain/sentence/entities/Sentence';
import { VoiceGenerationStatus } from '@prisma/client';
import CollapsePanel from './components/CollapsePanel';
import VoiceGenerateConfigBar from './components/VoiceGenerateConfigBar'; // Import the new component


const SpeechBuilder = ({ projectId }: {projectId: string}) => {
  // State from ProjectDetailPage
  const [newSentenceText, setNewSentenceText] = useState('');
  const [playingIndex, setPlayingIndex] = useState<number | null>(null); // State to track the index of the currently playing sentence
  const queryClient = useQueryClient();
  // Queries and Mutations for ProjectDetail (conditionally fetched)
  const { data: project, error: errorProjectDetails, refetch: refetchProjectDetails } = api.project.getById.useQuery(
    { projectId: projectId! },
    { enabled: !!projectId } // Only run query if selectedProjectId is not null
  );
 
  const createSentenceMutation = api.speech.sentence.createSentence.useMutation({
    onSuccess: () => {
      setNewSentenceText('');
      if (projectId) void refetchProjectDetails();
    },
  });
 
  const deleteSentenceMutation = api.speech.sentence.deleteSentence.useMutation({
    onSuccess: () => {
      if (projectId) void refetchProjectDetails();
    },
  });

  const updateSentenceMutation = api.speech.sentence.updateSentence.useMutation({
    onSuccess: () => {
      if (projectId) void refetchProjectDetails();
    },
  });
 
  const generateVoiceMutation = api.speech.generateVoice.useMutation({
    onSuccess: () => {
      // if (selectedProjectId) void refetchProjectDetails();
    },
  });
 
  const generateBatchSentenceMutation = api.speech.generateBatchSentence.useMutation({
    onSuccess: () => {
      if (projectId) void refetchProjectDetails();
    },
  });

  const updateProjectMutation = api.project.update.useMutation({
    onSuccess: () => {
      if (projectId) void refetchProjectDetails();
    },
  });
 
  const handleGenerateBatchSentence = () => {
    if (projectId) {
      generateBatchSentenceMutation.mutate({ projectId: projectId });
    }
  };
 
  const handleCreateSentence = () => {
    if (newSentenceText.trim() && projectId) {
      createSentenceMutation.mutate({ projectId: projectId, text: newSentenceText });
    }
  };
 
  const handleDeleteSentence = (sentenceId: string) => {
    deleteSentenceMutation.mutate({ sentenceId });
  };

  const handleUpdateSentenceConfig = (sentenceId: string, config: { voiceName?: string; seed?: number; model?: string; }) => {
    updateSentenceMutation.mutate({ sentenceId, ...config });
  };
 
  const handleGenerateVoice = (sentenceId: string) => {
    generateVoiceMutation.mutate({ sentenceId });
  };
 
  const handlePlayAllSentences = async () => {
    if (!project || project.sentences.length === 0) return;
 
    for (let i = 0; i < project.sentences.length; i++) {
      const sentence = project.sentences[i];
      if (sentence?.selectedVoiceGeneration?.audioUrl) {
        setPlayingIndex(i);
        const audio = new Audio(sentence.selectedVoiceGeneration.audioUrl);
        await new Promise<void>((resolve) => {
          audio.onended = () => resolve();
          audio.play().catch(error => {
            console.error("Error playing audio:", error);
            resolve(); // Resolve even on error to continue to the next sentence
          });
        });
      }
    }
    setPlayingIndex(null); // Reset after playing all
  };
 
  // Effect to refetch project details when selectedProjectId changes
  useEffect(() => {
    if (projectId) {
      void refetchProjectDetails();
    }
  }, [projectId, refetchProjectDetails]);
 
  api.speech.onQueueTaskUpdate.useSubscription(
    { projectId: projectId! },
    {
      onData(data) {
        queryClient.setQueryData([['project', 'getById'], { input: { projectId:  projectId! }, type: 'query'}],
          (oldProjectData: Project) => {
            if (!oldProjectData) return oldProjectData;
            return {
              ...oldProjectData,
              sentences: oldProjectData.sentences.map((sentence: Sentence) => {
                if (sentence.id === data.id) {
                  return data;
                }
                return sentence;
              }),
            }
          }
        )
      },
      enabled: !!projectId,
    }
  );
  
  const handleSaveProjectName = (newName: string) => {
    if (projectId && newName.trim() !== '') {
      updateProjectMutation.mutate({ projectId, name: newName });
    }
  };
  const handleSaveProjectDescription = (newDescription: string) => {
    if (projectId && newDescription.trim() !== '') {
      updateProjectMutation.mutate({ projectId, description: newDescription });
    }
  };

  return (
    <SpeechLayout
      sidebarContent={
        <SpeechSidebar
          selectedProjectId={projectId}
        />
      }
    >
      {!projectId && (
        <div className="flex flex-col items-center justify-center h-full text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <p className="text-xl">Select a project to view its details</p>
          <p className="text-sm mt-1">or create a new one from the sidebar.</p>
        </div>
      )}
 
      {/* Ensure this div takes up the remaining space if no project is selected */}
      {errorProjectDetails && projectId && (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-lg text-red-500">Error: {errorProjectDetails.message}</p>
        </div>
      )}
 
      {project && projectId && (
        <div className="flex flex-col h-full"> {/* Use flex column to manage space */}
          <div className="flex-grow overflow-y-auto max-w-full mx-auto px-4 sm:px-6 lg:px-8"> {/* Scrollable content area */}
            <div className="pb-6 border-b border-gray-300 mb-6">
              <Editable
                initialValue={project.name}
                onSave={handleSaveProjectName}
                textClassName="text-3xl font-bold text-indigo-700 sm:text-4xl"
                inputClassName="text-3xl font-bold text-indigo-700 sm:text-4xl bg-transparent border-b border-gray-400 focus:outline-none"
                displayTag="h1"
              />
              <Editable
                initialValue={project.description ?? "No description."}
                onSave={handleSaveProjectDescription}
                textClassName="mt-2 text-lg text-gray-600"
                inputClassName="mt-2 text-lg text-gray-600"
                displayTag="p"
              />
              <div className="mt-3 text-xs text-gray-500 space-x-4">
                <span>Created: {format(project.createdAt, "PP")}</span>
                <span>Last Modified: {format(project.updatedAt, "PP")}</span>
              </div>
              {/* <div className="mt-4 flex space-x-3">
                <Button size="small">Edit Project</Button>
                <Button variant="danger" size="small">Delete Project</Button>
              </div> */}
            </div>
 
            <div>
              <h2 className="text-2xl font-semibold text-gray-800 mb-5">Sentences</h2>
 
              {project.sentences.length === 0 && (
                <div className="text-gray-500 mb-5">No sentences added yet.</div>
              )}
 
              <ul className="space-y-6">
                {project.sentences.map((sentence) => (
                  <li key={sentence.id} className="py-4"> {/* Added bottom border and padding */}
                    <VoiceGenerateConfigBar className="mb-3" value={sentence.generateConfig} onChange={(value) => {
                      handleUpdateSentenceConfig(sentence.id, value);
                    }} />
                    <p className="text-sm font-medium text-gray-500 mb-3">{sentence.text}</p> {/* Main sentence text */}
                    <CollapsePanel disabled={sentence.voiceGenerations.length === 0} header={
                                    <div className="flex items-center space-x-2 text-sm">
                                      {sentence.selectedVoiceGeneration && (
                                      <>
                                        <div className="flex items-center"> {/* Play button and duration */}
                                          {sentence.selectedVoiceGeneration?.audioUrl ? (
                                            <audio controls src={sentence.selectedVoiceGeneration.audioUrl} className="ml-3 h-7"></audio>
                                          ): (
                                            <p className="text-sm text-gray-500"> {
                                              {
                                                [VoiceGenerationStatus.WAITING_USER]: 'Waiting...',
                                                [VoiceGenerationStatus.PENDING]: 'Pending...',
                                                [VoiceGenerationStatus.PROCESSING]: 'Processing...',
                                                [VoiceGenerationStatus.COMPLETED]: 'Completed',
                                                [VoiceGenerationStatus.FAILED]: 'Failed',
                                              }[sentence.selectedVoiceGeneration.status]}
                                            </p>
                                          )}
                                          {sentence.selectedVoiceGeneration.error && (
                                            <p className="text-sm text-red-500 ml-2">
                                              Error: {sentence.selectedVoiceGeneration.error}
                                            </p>
                                          )}
                                        </div>
                                      </>
                                    )}
                                      <IconButton
                                        size="small"
                                        onClick={() => handleGenerateVoice(sentence.id)}
                                        disabled={
                                          (generateVoiceMutation.isPending && generateVoiceMutation.variables?.sentenceId === sentence.id) ||
                                          (sentence.selectedVoiceGeneration?.status === VoiceGenerationStatus.PENDING )
                                        }
                                        className="p-1 bg-transparent hover:bg-gray-200 text-gray-600 hover:text-gray-800 border-none shadow-none"
                                        icon={BsSoundwave} // Using FaPlay for generate voice
                                        loading={generateVoiceMutation.isPending && generateVoiceMutation.variables?.sentenceId === sentence.id}
                                      />
                                      <IconButton
                                        size="small"
                                        variant="danger" // Keep danger variant for delete
                                        onClick={() => handleDeleteSentence(sentence.id)}
                                        disabled={deleteSentenceMutation.isPending && deleteSentenceMutation.variables?.sentenceId === sentence.id}
                                        className="p-1 bg-transparent hover:bg-red-200 text-red-00 hover:text-red-800 border-none shadow-none"
                                        icon={BsTrash}
                                        loading={deleteSentenceMutation.isPending && deleteSentenceMutation.variables?.sentenceId === sentence.id}
                                      />
                                      <IconButton size="small" className="p-1 bg-transparent hover:bg-gray-200 text-gray-600 hover:text-gray-800 border-none shadow-none" icon={FaEllipsisH} /> {/* More options button */}
                                    </div>
                          }>
                          {sentence.voiceGenerations.length > 0 && (
                                <div className="mt-3">
                                  <h3 className="text-sm font-medium text-gray-500 mb-1">Other Voice Generations</h3>
                                  <ul className="space-y-2">
                                    {sentence.voiceGenerations.map((voiceGeneration) => (
                                      <li key={voiceGeneration.id} className="flex items-center space-x-2">
                                        {voiceGeneration.audioUrl && (
                                          <audio controls src={voiceGeneration.audioUrl} className="ml-3 h-7"></audio>)
                                          }
                                        <p className="text-sm text-gray-500">{
                                          {
                                            [VoiceGenerationStatus.WAITING_USER]: 'Waiting...',
                                            [VoiceGenerationStatus.PENDING]: 'Pending...',
                                            [VoiceGenerationStatus.PROCESSING]: 'Processing...',
                                            [VoiceGenerationStatus.COMPLETED]: 'Completed',
                                            [VoiceGenerationStatus.FAILED]: 'Failed',
                                          }[voiceGeneration.status] ?? 'Unknown'
                                          }
                                          </p>
                                          {voiceGeneration.error && (
                                          <p className="text-sm text-red-500 ml-2">
                                            Error: {voiceGeneration.error}
                                          </p>)
                                          }
                                          {!!voiceGeneration.totalTokenCount && (
                                          <p className="text-sm text-gray-500 ml-2">
                                            Tokens: {voiceGeneration.totalTokenCount}
                                          </p>
                                          )}
                                          {!!voiceGeneration.model && (
                                          <p className="text-sm text-gray-500 ml-2">
                                            Model: {voiceGeneration.model}
                                          </p>
                                          )}
                                          {!!voiceGeneration.generateConfig && (
                                          <p className="text-sm text-gray-500 ml-2">
                                            Voice: {voiceGeneration.generateConfig.voiceName}
                                          </p>
                                          )}
                                          {!!voiceGeneration.generateConfig && (
                                          <p className="text-sm text-gray-500 ml-2">
                                            Seed: {voiceGeneration.generateConfig.seed}
                                          </p>
                                          )}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                    </CollapsePanel>  
                  </li>
                ))}
                {/* New sentence input row */}
                <li className="py-2 flex items-center gap-3"> {/* Removed background, shadow, border, and padding */}
                  <Input
                    id="newSentence"
                    type="text"
                    placeholder="Click to add new sentence"
                    value={newSentenceText}
                    onChange={(e) => setNewSentenceText(e.target.value)}
                    className="flex-grow text-md border-none focus:ring-0 p-0 placeholder-gray-400 italic" // Adjusted text size, removed default input styling, added placeholder styling
                  />
                  <Button
                    size="small" // Keep small size for the Add button
                    onClick={handleCreateSentence}
                    disabled={createSentenceMutation.isPending || !newSentenceText.trim()}
                    className="flex-shrink-0" // Prevent button from shrinking
                  >
                    {createSentenceMutation.isPending ? 'Adding...' : 'Add'}
                  </Button>
                </li>
              </ul>
            </div>
          </div>
          {/* Action Bar - Fixed at the bottom */}
          <div className="fixed bottom-0 right-0 p-4 bg-white shadow-lg flex justify-end space-x-4 w-full max-w-md"> {/* Added fixed positioning and styling */}
            <Button
              size="small"
              onClick={handleGenerateBatchSentence}
              disabled={generateBatchSentenceMutation.isPending || !projectId}
            >
              {generateBatchSentenceMutation.isPending ? 'Generating...' : 'Generate Batch Sentences'}
            </Button>
            <Button
              size="small"
              onClick={handlePlayAllSentences}
              disabled={!project || project.sentences.length === 0 || playingIndex !== null}
            >
              {playingIndex !== null ? `Playing ${playingIndex + 1}/${project?.sentences.length}` : 'Play All Sentences'}
            </Button>
          </div>
        </div>
      )}
    </SpeechLayout>
  );
};

export default SpeechBuilder;