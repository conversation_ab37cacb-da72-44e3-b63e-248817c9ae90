import { $Enums, PrismaClient } from "@prisma/client";
import type { IProjectRepository } from "~/domain/project/IProjectRepository";
import { Project } from "~/domain/project/entities/Project";
import type { ISentenceRepository, PrismaSentenceWithRelation } from "~/domain/sentence/ISentenceRepository";
import type {Project as PrismaProject} from '@prisma/client'

type PrismaProjectWithRelation = PrismaProject & { sentences: PrismaSentenceWithRelation[] }

export class PrismaProjectRepository implements IProjectRepository {
  constructor(private prisma: PrismaClient, private sentenceRepository: ISentenceRepository) {}

  async create(project: Project): Promise<void> {
    await this.prisma.project.create({data: project.toPersistence()})
  }

  async save(project: Project): Promise<void> {
    await this.prisma.project.upsert({
      where: { id: project.id },
      update: project.toPersistence(),
      create: project.toPersistence(),
    });
  }

  async getById(projectId: string): Promise<Project | null> {
    const project = await this.prisma.project.findUnique({
      where: { id: projectId },
      include: {
        sentences: {
          orderBy: { order: "asc" },
          include: {
            selectedVoiceGeneration: true,
            voiceGenerationQueueTasks: true,
          },
        },
      },
    });

    if (!project) {
      return null;
    }

    // TODO: Map Prisma project result back to domain entity if necessary
    // For now, returning the Prisma result directly for simplicity
    return this.mapPrismaProjectToDomain(project);
  }
  mapPrismaProjectToDomain(project: PrismaProjectWithRelation): Project | PromiseLike<Project | null> | null {
    return new Project(
      project.id,
      project.createdAt,
      project.updatedAt,
      {
        name: project.name,
        description: project.description,
        userId: project.userId,
        sentences: project.sentences.map((sentence) => {
          return this.sentenceRepository.mapPrismaSentenceToDomain(sentence);
        }),
      },
    )
  }
  async delete(projectId: string): Promise<void> {
    await this.prisma.project.delete({
      where: { id: projectId },
    });
  }
}