import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/infrastructure/trpc/trpc";
import { Prisma } from "@prisma/client";

const projectWithSentences = Prisma.validator<Prisma.ProjectDefaultArgs>()({
  include: {
    sentences: {
      include: {
        selectedVoiceGeneration: true,
      },
    },
  },
});

export type ProjectWithSentences = Prisma.ProjectGetPayload<typeof projectWithSentences>;

export const projectRouter = createTRPCRouter({
  getById: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.projectRepository.getById(input.projectId);
      return project?.toResponseObject();
    }),

  create: protectedProcedure
    .input(z.object({
      name: z.string().min(1, "Project name cannot be empty"),
      description: z.string().optional(),
      script: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const project = await ctx.createProjectUseCase.execute({
        name: input.name,
        description: input.description,
        script: input.script,
        userId: ctx.session.user.id,
      });
      return project;
    }),

  getAll: protectedProcedure
    .query(async ({ ctx }) => {
      return ctx.db.project.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: { createdAt: "desc" },
      });
    }),

  delete: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      await ctx.deleteProjectUseCase.execute(input.projectId);
      return { success: true };
    }),

  update: protectedProcedure
    .input(z.object({
      projectId: z.string(),
      name: z.string().min(1, "Project name cannot be empty").optional(),
      description: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const project = await ctx.updateProjectUseCase.execute({
        projectId: input.projectId,
        name: input.name,
        description: input.description,
      });
      return project;
    }),
});