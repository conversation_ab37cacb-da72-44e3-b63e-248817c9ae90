import { BaseEntity } from "~/domain/shared/BaseEntity";
import type { Sentence } from "~/domain/sentence/entities/Sentence";

interface ProjectProps {
  name: string;
  description?: string | null;
  userId: string;
  sentences?: Sentence[];
}

export class Project extends BaseEntity {
  private _name: string;
  private _description: string | null;
  private _userId: string;
  private _sentences: Sentence[];

  constructor(
    id: string,
    createdAt: Date,
    updatedAt: Date,
    { name, description, userId, sentences = [] }: ProjectProps,
  ) {
    super(id, createdAt, updatedAt);
    this._name = name;
    this._description = description ?? null;
    this._userId = userId;
    this._sentences = sentences;
  }

  static create(
    id: string,
    createdAt: Date,
    updatedAt: Date,
    props: ProjectProps,
  ): Project {
    return new Project(id, createdAt, updatedAt, props);
  }

  get name(): string {
    return this._name;
  }

  get description(): string | null {
    return this._description;
  }

  get userId(): string {
    return this._userId;
  }

  get sentences(): Sentence[] {
    return this._sentences;
  }

 public updateDescription(description: string | null): void {
   this._description = description ?? null;
   this.updatedAt = new Date();
 }

 public addSentence(sentence: Sentence): void {
   this._sentences.push(sentence);
   this.updatedAt = new Date();
 }

 set name(name: string) {
   this._name = name;
   this.updatedAt = new Date();
 }

 set sentences(sentences: Sentence[]) {
   this._sentences = sentences;
   this.updatedAt = new Date();
 }

  toPersistence(): {
    id: string | undefined;
    name: string;
    description: string | null;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
  } {
    return {
      id: this.id,
      name: this._name,
      description: this._description,
      userId: this._userId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  toResponseObject() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      name: this.name,
      description: this.description,
      userId: this.userId,
      sentences: this.sentences.map(sentence => sentence.toResponseObject()),
    };
  }
}