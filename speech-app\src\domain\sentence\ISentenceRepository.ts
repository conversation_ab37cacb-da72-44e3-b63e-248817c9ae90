import { Sentence } from "./entities/Sentence";
import { type Sentence as PrismaSentence, type VoiceGenerationQueue } from "@prisma/client";
export type PrismaSentenceWithRelation = PrismaSentence & { voiceGenerationQueueTasks: VoiceGenerationQueue[]; selectedVoiceGeneration?: VoiceGenerationQueue | null }
export interface ISentenceRepository {
  findById(id: string): Promise<Sentence | null>;
  save(sentence: Sentence): Promise<Sentence>;
  delete(id: string): Promise<void>;
  update(sentence: Sentence): Promise<Sentence>;
  findHighestOrder(projectId: string): Promise<number>;
  create(text: string, projectId: string): Promise<Sentence>;
  mapPrismaSentenceToDomain(sentence: PrismaSentenceWithRelation): Sentence;
  // Add other methods as needed, e.g., findByProjectId
}