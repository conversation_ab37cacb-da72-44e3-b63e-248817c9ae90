const CHUNK_PUBLIC_PATH = "server/app/_not-found/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/8e227_next_dist_8c9c5713._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__50a7c09c._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__67e65c2d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_5eea06ba._.js");
runtime.loadChunk("server/chunks/ssr/8e227_next_dist_client_components_forbidden-error_67f3bba2.js");
runtime.loadChunk("server/chunks/ssr/_e6f15de5._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.3_@babel+core@7.2_a86d708b2e372fcce1c9030a05f13bfc/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
