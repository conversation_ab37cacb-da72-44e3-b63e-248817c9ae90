@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vitest@3.1.4_@types+node@20_cbb0eab672cbd95a37c5210f9d849fb2\node_modules\vitest\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vitest@3.1.4_@types+node@20_cbb0eab672cbd95a37c5210f9d849fb2\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vitest@3.1.4_@types+node@20_cbb0eab672cbd95a37c5210f9d849fb2\node_modules\vitest\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vitest@3.1.4_@types+node@20_cbb0eab672cbd95a37c5210f9d849fb2\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\vitest.mjs" %*
)
