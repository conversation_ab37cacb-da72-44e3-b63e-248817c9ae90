import { ValueObject } from '../../shared/ValueObject'; // Assuming you'll create a ValueObject base class

type EmailProps = {
  value: string;
}

export class Email extends ValueObject<EmailProps> {
  private constructor(props: EmailProps) {
    super(props);
  }

  public static create(email: string): Email {
    if (!this.isValidEmail(email)) {
      throw new Error('Invalid email address');
    }
    return new Email({ value: email });
  }

  private static isValidEmail(email: string): boolean {
    // Basic email validation regex. Use a more robust one for production.
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  get value(): string {
    return this.props.value;
  }
}