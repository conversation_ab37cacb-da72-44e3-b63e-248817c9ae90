import * as fs from 'fs';
import { GoogleGenAI, type GenerateContentConfig } from '@google/genai';
import { TokenType } from '@prisma/client';
import { type ITTSGenerationService, type TTSGenerationConfig } from '~/domain/tts/ITTSGenerationService';

export class GoogleTTSGenerationService implements ITTSGenerationService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is not set in environment variables.');
    }
    this.ai = new GoogleGenAI({ apiKey });
  }
  async calTokenCount(text: string): Promise<number> {
    const countResult = await this.ai.models.countTokens({
      model: 'gemini-2.5-flash-preview-tts',
      contents: text,
      config: {
        
      },
    });
    return (countResult.totalTokens ?? 0) * 5;
  }

  async generateVoice(text: string, config?: TTSGenerationConfig){
    const response = await this.ai.models.generateContent({
      model: config?.model || 'gemini-2.5-flash-preview-tts',
      contents: [{ parts: [{ text }] }],
      config: {
        responseModalities: ['AUDIO'],
        seed: config?.seed,
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: config?.voiceName ?? 'Kore', // Use provided voiceName or default to 'Kore'
            },
          },
          languageCode: 'vi-VN',
        },
      },
    });

    const data = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;
    const totalTokenCount = response.usageMetadata?.totalTokenCount;

    if (!data) {
      throw new Error('No audio content received from Gemini API.');
    }

    return {
      buffer: Buffer.from(data, 'base64'),
      totalTokenCount: totalTokenCount || 0,
      tokenType: TokenType.GOOGLE_GENERATIVE_AI,
      model: 'gemini-2.5-flash-preview-tts'
    };
  }

  async generateVoiceStream(text: string, config?: TTSGenerationConfig) {
    const response = await this.ai.models.generateContentStream({
      model: config?.model || 'gemini-2.5-flash-preview-tts',
      contents: [{ parts: [{ text }] }],
      config: {
        responseModalities: ['AUDIO'],
        seed: config?.seed,
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: config?.voiceName || 'Kore', // Use provided voiceName or default to 'Kore'
            },
          },
          languageCode: 'vi-VN',
        },
      },
    });
    const allChunks: any[] = [];
    for await (const chunk of response) {
      allChunks.push(chunk);
      console.log(chunk);
    }
    await fs.promises.writeFile('tts_response.json', JSON.stringify(allChunks, null, 2));
    return response;
  }

}
