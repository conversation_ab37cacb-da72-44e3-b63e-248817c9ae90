import { PrismaClient, type VoiceGenerationQueue, VoiceGenerationStatus } from "@prisma/client";
import type { InAppQueueService } from "~/infrastructure/queue/InAppQueueService";
import { ee } from "~/utils/pubsub";

export class GenerateVoice {
  constructor(private prisma: PrismaClient, private queueService: InAppQueueService) {}

  async execute(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {
    // Check if the user already has a pending or processing task
    await this.prisma.voiceGenerationQueue.findFirst({
      where: {
        userId: userId,
        status: {
          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING],
        },
      },
    });

    // Create a new queue task
    const queueTask = await this.queueService.addTask(sentenceId, userId);

    ee.emit('queueTaskUpdate', queueTask);
    void this.queueService.processQueue();
    return queueTask;
  }


  async batchGenerate(projectId: string, userId: string): Promise<VoiceGenerationQueue[]> {
    const project = await this.prisma.project.findUnique({
      where: { id: projectId },
      include: {
        sentences: true,
      },
    });
  
    if (!project) {
      throw new Error('Project not found');
    }
  
    const sentences = project.sentences.map((sentence) => sentence.id);
    const queueTasks: VoiceGenerationQueue[] = [];
    for (const sentenceId of sentences) {
      const task = await this.execute(sentenceId, userId);
      queueTasks.push(task);
    }
    return queueTasks;
  }
  
  
}
