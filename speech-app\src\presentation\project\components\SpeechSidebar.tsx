import { ContextDropdown } from '../../@shared/components/ContextDropdown';
import React, { useEffect, useState, useRef } from 'react';
import { api } from "~/infrastructure/trpc/react";
import { format } from "date-fns";
import Link from 'next/link';
import LinkButton from '~/presentation/@shared/components/LinkButton';
import IconButton from '~/presentation/@shared/components/IconButton';

interface SpeechSidebarProps {
  selectedProjectId?: string | null;
}

const SpeechSidebar: React.FC<SpeechSidebarProps> = ({ selectedProjectId }) => {
  const { data: projects, isLoading: isLoadingProjects, error: errorProjects, refetch: refetchProjects } = api.project.getAll.useQuery();
  const deleteProjectMutation = api.project.delete.useMutation();
  const updateProjectMutation = api.project.update.useMutation();

  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [newProjectName, setNewProjectName] = useState<string>('');
  const menuRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Close sidebar when a project is selected on mobile
  useEffect(() => {
    if (selectedProjectId && window.innerWidth < 768) { // 768px is md breakpoint
      // This logic should ideally be in SpeechLayout or handled by a global state/context
      // For now, keeping it here as it was in SpeechBuilder
      // setIsSidebarOpen(false); // This state is in SpeechLayout now
    }
  }, [selectedProjectId]);



  return (
    <>
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-slate-800">Projects</h1>
        <LinkButton href="/projects" type="primary">
          + New
        </LinkButton>
      </header>

      {isLoadingProjects && <p className="text-center py-5 text-gray-500 text-sm">Loading projects...</p>}
      {errorProjects && <p className="text-center py-5 text-rose-500 text-sm">Error: {errorProjects.message}</p>}
      {projects?.length === 0 && !isLoadingProjects && (
        <div className="text-center py-5 text-gray-500 text-sm">
          No projects yet.
        </div>
      )}
      <ul className="space-y-3">
        {projects?.map((p) => (
          <li key={p.id} className="relative group">
            <Link
              href={`/projects/${p.id}`}
              className={`w-full block text-left p-3 rounded-md hover:bg-indigo-100/50 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-150
                ${selectedProjectId === p.id ? 'bg-indigo-100 text-indigo-700 font-semibold' : 'hover:bg-slate-200 text-slate-700'}`}
            >
              {editingProjectId === p.id ? (
                <input
                  ref={inputRef}
                  type="text"
                  value={newProjectName}
                  onChange={(e) => setNewProjectName(e.target.value)}
                  onBlur={async () => {
                    if (newProjectName.trim() !== '' && newProjectName !== p.name) {
                      await updateProjectMutation.mutateAsync({
                        projectId: p.id,
                        name: newProjectName.trim(),
                      });
                      refetchProjects();
                    }
                    setEditingProjectId(null);
                  }}
                  onKeyDown={async (e) => {
                    if (e.key === 'Enter') {
                      inputRef.current?.blur(); // Trigger onBlur
                    } else if (e.key === 'Escape') {
                      setEditingProjectId(null);
                    }
                  }}
                  className="w-full bg-white border border-gray-300 rounded-md px-2 py-1 text-md font-medium focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              ) : (
                <>
                  <h3 className="text-md font-medium">{p.name}</h3>
                  <p className="text-xs text-gray-500">
                    {p.description ? (p.description.length > 30 ? p.description.substring(0, 30) + '...' : p.description) : "No description"}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Last updated: {format(new Date(p.updatedAt), "MMM dd, yyyy")}
                  </p>
                </>
              )}
            </Link>

            <div className="absolute top-2 right-2">
              <ContextDropdown
                trigger={
                  <IconButton
                    icon={() => <span className="h-5 w-5 text-gray-500">...</span>}
                    aria-label="Project options"
                  />
                }
              >
                <button
                  onClick={async () => {
                    if (confirm(`Are you sure you want to delete project "${p.name}"?`)) {
                      await deleteProjectMutation.mutateAsync({ projectId: p.id });
                      refetchProjects();
                    }
                  }}
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left transition-colors duration-150 ease-in-out"
                >
                  Delete
                </button>
                <button
                  onClick={() => {
                    setEditingProjectId(p.id);
                    setNewProjectName(p.name);
                    // Focus the input after it's rendered
                    setTimeout(() => inputRef.current?.focus(), 0);
                  }}
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left transition-colors duration-150 ease-in-out"
                >
                  Rename
                </button>
              </ContextDropdown>
            </div>
          </li>
        ))}
      </ul>
    </>
  );
};

export default SpeechSidebar;