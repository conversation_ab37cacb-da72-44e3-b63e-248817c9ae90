{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    AUTH_SECRET:\n      process.env.NODE_ENV === \"production\"\n        ? z.string().optional()\n        : z.string().optional(),\n    GOOGLE_CLIENT_ID: z.string().optional(),\n    GOOGLE_CLIENT_SECRET: z.string().optional(),\n    DATABASE_URL: z.string().url().optional(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    // NEXT_PUBLIC_CLIENTVAR: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    AUTH_SECRET: process.env.AUTH_SECRET,\n    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,\n    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,MAAM,CAAA,GAAA,wRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,aACE,6EAEI,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,kBAAkB,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,sBAAsB,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzC,cAAc,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACvC,UAAU,qOAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;IAER;IAEA;;;GAGC,GACD,YAAY;QACV,aAAa,QAAQ,GAAG,CAAC,WAAW;QACpC,kBAAkB,QAAQ,GAAG,CAAC,gBAAgB;QAC9C,sBAAsB,QAAQ,GAAG,CAAC,oBAAoB;QACtD,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;IACV;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/persistence/prisma/client.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\nimport { env } from \"~/env\";\n\nconst createPrismaClient = () =>\n  new PrismaClient({\n    log:\n      env.NODE_ENV === \"development\" ? [\n        // \"query\", \n        \"error\", \n        // \"warn\"\n      ] : [\"error\"],\n  });\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: ReturnType<typeof createPrismaClient> | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? createPrismaClient();\n\nif (env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM,qBAAqB,IACzB,IAAI,6HAAA,CAAA,eAAY,CAAC;QACf,KACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBAAgB;YAC/B,YAAY;YACZ;SAED,GAAG;YAAC;SAAQ;IACjB;AAEF,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI;AAE5C,IAAI,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,cAAc,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/auth/config.ts"], "sourcesContent": ["import { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport { type DefaultSession, type NextAuthConfig } from \"next-auth\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\nimport GoogleProvider from \"next-auth/providers/google\";\nimport { compare } from \"bcryptjs\";\n\nimport { db } from \"../persistence/prisma/client\";\n\n/**\n * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`\n * object and keep type safety.\n *\n * @see https://next-auth.js.org/getting-started/typescript#module-augmentation\n */\ndeclare module \"next-auth\" {\n  interface Session extends DefaultSession {\n    user: {\n      id: string;\n      name?: string | null;\n      email?: string | null;\n      image?: string | null;\n    } & DefaultSession[\"user\"];\n  }\n}\n\n/**\n * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.\n *\n * @see https://next-auth.js.org/configuration/options\n */\nexport const authConfig = {\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n    }),\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"text\" },\n        password: { label: \"Password\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        const user = await db.user.findUnique({\n          where: { email: credentials.email as string },\n        });\n\n        if (!user || !user.password) {\n          return null;\n        }\n\n        const isValidPassword = await compare(credentials.password as string, user.password);\n\n        if (!isValidPassword) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n        };\n      },\n    }),\n    /**\n     * ...add more providers here.\n     *\n     * Most other providers require a bit more work than the Discord provider. For example, the\n     * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account\n     * model. Refer to the NextAuth.js docs for the provider you want to use. Example:\n     *\n     * @see https://next-auth.js.org/providers/github\n     */\n  ],\n  adapter: PrismaAdapter(db),\n  callbacks: {\n    session: ({ session, user }) => ({\n      ...session,\n      user: {\n        ...session.user,\n        id: user.id,\n      },\n    }),\n  },\n  pages: {\n    signIn: \"/auth/login\", // Specify the login page\n    // You can also specify other pages like signOut, error, verifyRequest, newUser\n  },\n} satisfies NextAuthConfig;\n"], "names": [], "mappings": ";;;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AAEA;;;;;;AAwBO,MAAM,aAAa;IACxB,WAAW;QACT,CAAA,GAAA,mNAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,wNAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;gBACtC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,0JAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBAAE,OAAO,YAAY,KAAK;oBAAW;gBAC9C;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,wLAAA,CAAA,UAAO,AAAD,EAAE,YAAY,QAAQ,EAAY,KAAK,QAAQ;gBAEnF,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;gBACnB;YACF;QACF;KAUD;IACD,SAAS,CAAA,GAAA,mQAAA,CAAA,gBAAa,AAAD,EAAE,0JAAA,CAAA,KAAE;IACzB,WAAW;QACT,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAK,CAAC;gBAC/B,GAAG,OAAO;gBACV,MAAM;oBACJ,GAAG,QAAQ,IAAI;oBACf,IAAI,KAAK,EAAE;gBACb;YACF,CAAC;IACH;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/auth/index.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport { cache } from \"react\";\r\nimport { authConfig } from \"./config\";\r\n\r\n\r\n\r\nconst { auth: uncachedAuth, handlers, signIn, signOut } = NextAuth(authConfig);\r\n\r\nconst auth = cache(uncachedAuth);\r\n\r\nexport { auth, handlers, signIn, signOut };\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AACA;;;;AAIA,MAAM,EAAE,MAAM,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mQAAA,CAAA,UAAQ,AAAD,EAAE,yIAAA,CAAA,aAAU;AAE7E,MAAM,OAAO,CAAA,GAAA,sTAAA,CAAA,QAAK,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/shared/BaseEntity.ts"], "sourcesContent": ["export abstract class BaseEntity {\r\n  public readonly id: string;\r\n  public readonly createdAt: Date;\r\n  public updatedAt: Date;\r\n\r\n  constructor(id: string, createdAt: Date, updatedAt: Date) {\r\n    this.id = id;\r\n    this.createdAt = createdAt;\r\n    this.updatedAt = updatedAt;\r\n  }\r\n\r\n  public equals(entity?: BaseEntity): boolean {\r\n    if (entity === null || entity === undefined) {\r\n      return false;\r\n    }\r\n    if (this === entity) {\r\n      return true;\r\n    }\r\n    if (!this.isEntity(entity)) {\r\n      return false;\r\n    }\r\n    return this.id === entity.id;\r\n  }\r\n\r\n  private isEntity(v: unknown): v is BaseEntity {\r\n    return v instanceof BaseEntity;\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAO,MAAe;IACJ,GAAW;IACX,UAAgB;IACzB,UAAgB;IAEvB,YAAY,EAAU,EAAE,SAAe,EAAE,SAAe,CAAE;QACxD,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;IACnB;IAEO,OAAO,MAAmB,EAAW;QAC1C,IAAI,WAAW,QAAQ,WAAW,WAAW;YAC3C,OAAO;QACT;QACA,IAAI,IAAI,KAAK,QAAQ;YACnB,OAAO;QACT;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC1B,OAAO;QACT;QACA,OAAO,IAAI,CAAC,EAAE,KAAK,OAAO,EAAE;IAC9B;IAEQ,SAAS,CAAU,EAAmB;QAC5C,OAAO,aAAa;IACtB;AACF", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/project/entities/Project.ts"], "sourcesContent": ["import { BaseEntity } from \"~/domain/shared/BaseEntity\";\r\nimport type { Sentence } from \"~/domain/sentence/entities/Sentence\";\r\n\r\ninterface ProjectProps {\r\n  name: string;\r\n  description?: string | null;\r\n  userId: string;\r\n  sentences?: Sentence[];\r\n}\r\n\r\nexport class Project extends BaseEntity {\r\n  private _name: string;\r\n  private _description: string | null;\r\n  private _userId: string;\r\n  private _sentences: Sentence[];\r\n\r\n  constructor(\r\n    id: string,\r\n    createdAt: Date,\r\n    updatedAt: Date,\r\n    { name, description, userId, sentences = [] }: ProjectProps,\r\n  ) {\r\n    super(id, createdAt, updatedAt);\r\n    this._name = name;\r\n    this._description = description ?? null;\r\n    this._userId = userId;\r\n    this._sentences = sentences;\r\n  }\r\n\r\n  static create(\r\n    id: string,\r\n    createdAt: Date,\r\n    updatedAt: Date,\r\n    props: ProjectProps,\r\n  ): Project {\r\n    return new Project(id, createdAt, updatedAt, props);\r\n  }\r\n\r\n  get name(): string {\r\n    return this._name;\r\n  }\r\n\r\n  get description(): string | null {\r\n    return this._description;\r\n  }\r\n\r\n  get userId(): string {\r\n    return this._userId;\r\n  }\r\n\r\n  get sentences(): Sentence[] {\r\n    return this._sentences;\r\n  }\r\n\r\n public updateDescription(description: string | null): void {\r\n   this._description = description ?? null;\r\n   this.updatedAt = new Date();\r\n }\r\n\r\n public addSentence(sentence: Sentence): void {\r\n   this._sentences.push(sentence);\r\n   this.updatedAt = new Date();\r\n }\r\n\r\n set name(name: string) {\r\n   this._name = name;\r\n   this.updatedAt = new Date();\r\n }\r\n\r\n set sentences(sentences: Sentence[]) {\r\n   this._sentences = sentences;\r\n   this.updatedAt = new Date();\r\n }\r\n\r\n  toPersistence(): {\r\n    id: string | undefined;\r\n    name: string;\r\n    description: string | null;\r\n    userId: string;\r\n    createdAt: Date;\r\n    updatedAt: Date;\r\n  } {\r\n    return {\r\n      id: this.id,\r\n      name: this._name,\r\n      description: this._description,\r\n      userId: this._userId,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  toResponseObject() {\r\n    return {\r\n      id: this.id,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n      name: this.name,\r\n      description: this.description,\r\n      userId: this.userId,\r\n      sentences: this.sentences.map(sentence => sentence.toResponseObject()),\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,gBAAgB,uIAAA,CAAA,aAAU;IAC7B,MAAc;IACd,aAA4B;IAC5B,QAAgB;IAChB,WAAuB;IAE/B,YACE,EAAU,EACV,SAAe,EACf,SAAe,EACf,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EAAgB,CAC3D;QACA,KAAK,CAAC,IAAI,WAAW;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,YAAY,GAAG,eAAe;QACnC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,OAAO,OACL,EAAU,EACV,SAAe,EACf,SAAe,EACf,KAAmB,EACV;QACT,OAAO,IAAI,QAAQ,IAAI,WAAW,WAAW;IAC/C;IAEA,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,IAAI,cAA6B;QAC/B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,IAAI,SAAiB;QACnB,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,IAAI,YAAwB;QAC1B,OAAO,IAAI,CAAC,UAAU;IACxB;IAEM,kBAAkB,WAA0B,EAAQ;QACzD,IAAI,CAAC,YAAY,GAAG,eAAe;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;IAEO,YAAY,QAAkB,EAAQ;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;IAEA,IAAI,KAAK,IAAY,EAAE;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;IAEA,IAAI,UAAU,SAAqB,EAAE;QACnC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;IAEC,gBAOE;QACA,OAAO;YACL,IAAI,IAAI,CAAC,EAAE;YACX,MAAM,IAAI,CAAC,KAAK;YAChB,aAAa,IAAI,CAAC,YAAY;YAC9B,QAAQ,IAAI,CAAC,OAAO;YACpB,WAAW,IAAI,CAAC,SAAS;YACzB,WAAW,IAAI,CAAC,SAAS;QAC3B;IACF;IAEA,mBAAmB;QACjB,OAAO;YACL,IAAI,IAAI,CAAC,EAAE;YACX,WAAW,IAAI,CAAC,SAAS;YACzB,WAAW,IAAI,CAAC,SAAS;YACzB,MAAM,IAAI,CAAC,IAAI;YACf,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;YACnB,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,WAAY,SAAS,gBAAgB;QACrE;IACF;AACF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/persistence/repositories/PrismaProjectRepository.ts"], "sourcesContent": ["import { $Enums, PrismaClient } from \"@prisma/client\";\r\nimport type { IProjectRepository } from \"~/domain/project/IProjectRepository\";\r\nimport { Project } from \"~/domain/project/entities/Project\";\r\nimport type { ISentenceRepository, PrismaSentenceWithRelation } from \"~/domain/sentence/ISentenceRepository\";\r\nimport type {Project as PrismaProject} from '@prisma/client'\r\n\r\ntype PrismaProjectWithRelation = PrismaProject & { sentences: PrismaSentenceWithRelation[] }\r\n\r\nexport class PrismaProjectRepository implements IProjectRepository {\r\n  constructor(private prisma: PrismaClient, private sentenceRepository: ISentenceRepository) {}\r\n\r\n  async create(project: Project): Promise<void> {\r\n    await this.prisma.project.create({data: project.toPersistence()})\r\n  }\r\n\r\n  async save(project: Project): Promise<void> {\r\n    await this.prisma.project.upsert({\r\n      where: { id: project.id },\r\n      update: project.toPersistence(),\r\n      create: project.toPersistence(),\r\n    });\r\n  }\r\n\r\n  async getById(projectId: string): Promise<Project | null> {\r\n    const project = await this.prisma.project.findUnique({\r\n      where: { id: projectId },\r\n      include: {\r\n        sentences: {\r\n          orderBy: { order: \"asc\" },\r\n          include: {\r\n            selectedVoiceGeneration: true,\r\n            voiceGenerationQueueTasks: true,\r\n          },\r\n        },\r\n      },\r\n    });\r\n\r\n    if (!project) {\r\n      return null;\r\n    }\r\n\r\n    // TODO: Map Prisma project result back to domain entity if necessary\r\n    // For now, returning the Prisma result directly for simplicity\r\n    return this.mapPrismaProjectToDomain(project);\r\n  }\r\n  mapPrismaProjectToDomain(project: PrismaProjectWithRelation): Project | PromiseLike<Project | null> | null {\r\n    return new Project(\r\n      project.id,\r\n      project.createdAt,\r\n      project.updatedAt,\r\n      {\r\n        name: project.name,\r\n        description: project.description,\r\n        userId: project.userId,\r\n        sentences: project.sentences.map((sentence) => {\r\n          return this.sentenceRepository.mapPrismaSentenceToDomain(sentence);\r\n        }),\r\n      },\r\n    )\r\n  }\r\n  async delete(projectId: string): Promise<void> {\r\n    await this.prisma.project.delete({\r\n      where: { id: projectId },\r\n    });\r\n  }\r\n}"], "names": [], "mappings": ";;;AAEA;;AAMO,MAAM;;;IACX,YAAY,AAAQ,MAAoB,EAAE,AAAQ,kBAAuC,CAAE;aAAvE,SAAA;aAA8B,qBAAA;IAA0C;IAE5F,MAAM,OAAO,OAAgB,EAAiB;QAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAAC,MAAM,QAAQ,aAAa;QAAE;IACjE;IAEA,MAAM,KAAK,OAAgB,EAAiB;QAC1C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI,QAAQ,EAAE;YAAC;YACxB,QAAQ,QAAQ,aAAa;YAC7B,QAAQ,QAAQ,aAAa;QAC/B;IACF;IAEA,MAAM,QAAQ,SAAiB,EAA2B;QACxD,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,WAAW;oBACT,SAAS;wBAAE,OAAO;oBAAM;oBACxB,SAAS;wBACP,yBAAyB;wBACzB,2BAA2B;oBAC7B;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,qEAAqE;QACrE,+DAA+D;QAC/D,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACvC;IACA,yBAAyB,OAAkC,EAAgD;QACzG,OAAO,IAAI,iJAAA,CAAA,UAAO,CAChB,QAAQ,EAAE,EACV,QAAQ,SAAS,EACjB,QAAQ,SAAS,EACjB;YACE,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,QAAQ,QAAQ,MAAM;YACtB,WAAW,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC;gBAChC,OAAO,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC;YAC3D;QACF;IAEJ;IACA,MAAM,OAAO,SAAiB,EAAiB;QAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,OAAO;gBAAE,IAAI;YAAU;QACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/sentence/entities/Sentence.ts"], "sourcesContent": ["import { BaseEntity } from \"~/domain/shared/BaseEntity\";\r\nimport type { VoiceGeneration } from \"~/domain/voiceGeneration/entities/VoiceGeneration\";\r\n\r\nexport class Sentence extends BaseEntity {\r\n  text: string;\r\n  order: number;\r\n  tone?: string;\r\n  emotion?: string;\r\n  audioUrl?: string; // Deprecated, moved to VoiceGeneration\r\n  audioDuration?: number; // Deprecated, moved to VoiceGeneration\r\n  status: string;\r\n  projectId: string;\r\n  voiceGenerations: VoiceGeneration[];\r\n  selectedVoiceGenerationId?: string;\r\n  selectedVoiceGeneration?: VoiceGeneration;\r\n  generateConfig: {\r\n    voiceName: string;\r\n    seed: number;\r\n    model: string;\r\n  };\r\n \r\n  constructor(\r\n    id: string,\r\n    text: string,\r\n    order: number,\r\n    status: string,\r\n    projectId: string,\r\n    createdAt: Date,\r\n    updatedAt: Date,\r\n    tone?: string,\r\n    emotion?: string,\r\n    audioUrl?: string,\r\n    audioDuration?: number,\r\n    voiceGenerations: VoiceGeneration[] = [],\r\n    selectedVoiceGenerationId?: string,\r\n    selectedVoiceGeneration?: VoiceGeneration,\r\n    generateConfig: { voiceName: string; seed: number; model: string; } = { voiceName: '', seed: 0, model: '' },\r\n  ) {\r\n    super(id, createdAt, updatedAt);\r\n    this.text = text;\r\n    this.order = order;\r\n    this.tone = tone;\r\n    this.emotion = emotion;\r\n    this.audioUrl = audioUrl;\r\n    this.audioDuration = audioDuration;\r\n    this.status = status;\r\n    this.projectId = projectId;\r\n    this.voiceGenerations = voiceGenerations;\r\n    this.selectedVoiceGenerationId = selectedVoiceGenerationId;\r\n    this.selectedVoiceGeneration = selectedVoiceGeneration;\r\n    this.generateConfig = generateConfig;\r\n  }\r\n \r\n  toResponseObject() {\r\n    return {\r\n      id: this.id,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n      text: this.text,\r\n      order: this.order,\r\n      tone: this.tone,\r\n      emotion: this.emotion,\r\n      audioUrl: this.audioUrl,\r\n      audioDuration: this.audioDuration,\r\n      status: this.status,\r\n      projectId: this.projectId,\r\n      voiceGenerations: this.voiceGenerations.map(vg => vg.toResponseObject()),\r\n      selectedVoiceGenerationId: this.selectedVoiceGenerationId,\r\n      selectedVoiceGeneration: this.selectedVoiceGeneration?.toResponseObject(),\r\n      generateConfig: this.generateConfig,\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,iBAAiB,uIAAA,CAAA,aAAU;IACtC,KAAa;IACb,MAAc;IACd,KAAc;IACd,QAAiB;IACjB,SAAkB;IAClB,cAAuB;IACvB,OAAe;IACf,UAAkB;IAClB,iBAAoC;IACpC,0BAAmC;IACnC,wBAA0C;IAC1C,eAIE;IAEF,YACE,EAAU,EACV,IAAY,EACZ,KAAa,EACb,MAAc,EACd,SAAiB,EACjB,SAAe,EACf,SAAe,EACf,IAAa,EACb,OAAgB,EAChB,QAAiB,EACjB,aAAsB,EACtB,mBAAsC,EAAE,EACxC,yBAAkC,EAClC,uBAAyC,EACzC,iBAAsE;QAAE,WAAW;QAAI,MAAM;QAAG,OAAO;IAAG,CAAC,CAC3G;QACA,KAAK,CAAC,IAAI,WAAW;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,yBAAyB,GAAG;QACjC,IAAI,CAAC,uBAAuB,GAAG;QAC/B,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA,mBAAmB;QACjB,OAAO;YACL,IAAI,IAAI,CAAC,EAAE;YACX,WAAW,IAAI,CAAC,SAAS;YACzB,WAAW,IAAI,CAAC,SAAS;YACzB,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,KAAK;YACjB,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,UAAU,IAAI,CAAC,QAAQ;YACvB,eAAe,IAAI,CAAC,aAAa;YACjC,QAAQ,IAAI,CAAC,MAAM;YACnB,WAAW,IAAI,CAAC,SAAS;YACzB,kBAAkB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,gBAAgB;YACrE,2BAA2B,IAAI,CAAC,yBAAyB;YACzD,yBAAyB,IAAI,CAAC,uBAAuB,EAAE;YACvD,gBAAgB,IAAI,CAAC,cAAc;QACrC;IACF;AACF", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/voiceGeneration/entities/VoiceGeneration.ts"], "sourcesContent": ["import { BaseEntity } from \"~/domain/shared/BaseEntity\";\r\n\r\nexport class VoiceGeneration extends BaseEntity {\r\n  audioUrl: string | null;\r\n  audioDuration?: number | null;\r\n  sentenceId: string;\r\n  status: string;\r\n  error: string | null;\r\n  userId: string;\r\n  startedAt: Date | null;\r\n  completedAt: Date | null;\r\n\r\n  totalTokenCount: number;\r\n  tokenType: string | null;\r\n  model: string | null;\r\n\r\n  generateConfig: {\r\n    voiceName: string;\r\n    seed: number;\r\n    model: string;\r\n  };\r\n\r\n  constructor(\r\n    id: string,\r\n    audioUrl: string | null,\r\n    audioDuration: number | undefined,\r\n    status: string,\r\n    createdAt: Date,\r\n    sentenceId: string,\r\n    error: string | null,\r\n    userId: string,\r\n    startedAt: Date | null,\r\n    completedAt: Date | null,\r\n    totalTokenCount: number,\r\n    tokenType: string | null,\r\n    model: string | null,\r\n    updatedAt?: Date, // Make updatedAt optional as it might not be present on creation\r\n    generateConfig: { voiceName: string; seed: number; model: string; } = { voiceName: '', seed: 0, model: '' },\r\n  ) {\r\n    super(id, createdAt, updatedAt ?? new Date()); // Pass updatedAt to super, default to new Date() if undefined\r\n    this.audioUrl = audioUrl;\r\n    this.audioDuration = audioDuration;\r\n    this.sentenceId = sentenceId;\r\n    this.status = status;\r\n    this.error = error;\r\n    this.userId = userId;\r\n    this.startedAt = startedAt;\r\n    this.totalTokenCount = totalTokenCount;\r\n    this.tokenType = tokenType;\r\n    this.model = model;\r\n    this.completedAt = completedAt;\r\n    this.generateConfig = generateConfig;\r\n  }\r\n\r\n  toResponseObject() {\r\n    return {\r\n      id: this.id,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n      audioUrl: this.audioUrl,\r\n      audioDuration: this.audioDuration,\r\n      sentenceId: this.sentenceId,\r\n      status: this.status,\r\n      error: this.error,\r\n      userId: this.userId,\r\n      startedAt: this.startedAt,\r\n      completedAt: this.completedAt,\r\n      totalTokenCount: this.totalTokenCount,\r\n      tokenType: this.tokenType,\r\n      model: this.model,\r\n      generateConfig: this.generateConfig,\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,wBAAwB,uIAAA,CAAA,aAAU;IAC7C,SAAwB;IACxB,cAA8B;IAC9B,WAAmB;IACnB,OAAe;IACf,MAAqB;IACrB,OAAe;IACf,UAAuB;IACvB,YAAyB;IAEzB,gBAAwB;IACxB,UAAyB;IACzB,MAAqB;IAErB,eAIE;IAEF,YACE,EAAU,EACV,QAAuB,EACvB,aAAiC,EACjC,MAAc,EACd,SAAe,EACf,UAAkB,EAClB,KAAoB,EACpB,MAAc,EACd,SAAsB,EACtB,WAAwB,EACxB,eAAuB,EACvB,SAAwB,EACxB,KAAoB,EACpB,SAAgB,EAChB,iBAAsE;QAAE,WAAW;QAAI,MAAM;QAAG,OAAO;IAAG,CAAC,CAC3G;QACA,KAAK,CAAC,IAAI,WAAW,aAAa,IAAI,SAAS,8DAA8D;QAC7G,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA,mBAAmB;QACjB,OAAO;YACL,IAAI,IAAI,CAAC,EAAE;YACX,WAAW,IAAI,CAAC,SAAS;YACzB,WAAW,IAAI,CAAC,SAAS;YACzB,UAAU,IAAI,CAAC,QAAQ;YACvB,eAAe,IAAI,CAAC,aAAa;YACjC,YAAY,IAAI,CAAC,UAAU;YAC3B,QAAQ,IAAI,CAAC,MAAM;YACnB,OAAO,IAAI,CAAC,KAAK;YACjB,QAAQ,IAAI,CAAC,MAAM;YACnB,WAAW,IAAI,CAAC,SAAS;YACzB,aAAa,IAAI,CAAC,WAAW;YAC7B,iBAAiB,IAAI,CAAC,eAAe;YACrC,WAAW,IAAI,CAAC,SAAS;YACzB,OAAO,IAAI,CAAC,KAAK;YACjB,gBAAgB,IAAI,CAAC,cAAc;QACrC;IACF;AACF", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/persistence/repositories/PrismaSentenceRepository.ts"], "sourcesContent": ["import { PrismaClient, type VoiceGenerationQueue } from \"@prisma/client\";\r\nimport { type ISentenceRepository, type PrismaSentenceWithRelation } from \"~/domain/sentence/ISentenceRepository\";\r\nimport { Sentence } from \"~/domain/sentence/entities/Sentence\";\r\nimport { VoiceGeneration } from \"~/domain/voiceGeneration/entities/VoiceGeneration\";\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { type Sentence as PrismaSentence } from \"@prisma/client\";\r\n\r\n\r\nexport class PrismaSentenceRepository implements ISentenceRepository {\r\n  constructor(private prisma: PrismaClient) {}\r\n\r\n  async create(text: string, projectId: string): Promise<Sentence> {\r\n    const newSentence = await this.prisma.$transaction(async (prisma) => {\r\n      const result = await prisma.sentence.aggregate({\r\n        where: { projectId },\r\n        _max: {\r\n          order: true,\r\n        },\r\n      });\r\n      const newOrder = (result._max.order ?? 0) + 1;\r\n\r\n      return prisma.sentence.create({\r\n        data: {\r\n          id: uuidv4(),\r\n          text: text,\r\n          order: newOrder,\r\n          status: 'Draft', // Default status\r\n          projectId: projectId,\r\n          generateConfig: { voiceName: 'Kore', seed: randomSeed(), model: 'gemini-2.5-flash-preview-tts' },\r\n        },\r\n        include: {\r\n          voiceGenerationQueueTasks: true,\r\n          selectedVoiceGeneration: true,\r\n        },\r\n      });\r\n    });\r\n\r\n    return this.mapPrismaSentenceToDomain(newSentence);\r\n  }\r\n\r\n\r\n  async findById(id: string): Promise<Sentence | null> {\r\n    const sentence = await this.prisma.sentence.findUnique({\r\n      where: { id },\r\n      include: {\r\n        voiceGenerationQueueTasks: true,\r\n        selectedVoiceGeneration: true,\r\n      },\r\n    });\r\n\r\n    if (!sentence) {\r\n      return null;\r\n    }\r\n\r\n    return this.mapPrismaSentenceToDomain(sentence);\r\n  }\r\n\r\n  async save(sentence: Sentence): Promise<Sentence> {\r\n    const savedSentence = await this.prisma.sentence.upsert({\r\n      where: { id: sentence.id },\r\n      update: {\r\n        text: sentence.text,\r\n        order: sentence.order,\r\n        tone: sentence.tone,\r\n        emotion: sentence.emotion,\r\n        audioUrl: sentence.audioUrl,\r\n        audioDuration: sentence.audioDuration,\r\n        status: sentence.status,\r\n        projectId: sentence.projectId,\r\n        selectedVoiceGenerationId: sentence.selectedVoiceGenerationId,\r\n        updatedAt: new Date(),\r\n        generateConfig: sentence.generateConfig as any, // Update generateConfig\r\n      },\r\n      create: {\r\n        id: sentence.id,\r\n        text: sentence.text,\r\n        order: sentence.order,\r\n        tone: sentence.tone,\r\n        emotion: sentence.emotion,\r\n        audioUrl: sentence.audioUrl,\r\n        audioDuration: sentence.audioDuration,\r\n        status: sentence.status,\r\n        projectId: sentence.projectId,\r\n        selectedVoiceGenerationId: sentence.selectedVoiceGenerationId,\r\n        generateConfig: sentence.generateConfig as any, // Create generateConfig\r\n      },\r\n      include: {\r\n        voiceGenerationQueueTasks: true,\r\n        selectedVoiceGeneration: true,\r\n      },\r\n    });\r\n\r\n    return this.mapPrismaSentenceToDomain(savedSentence);\r\n  }\r\n\r\n  async update(sentence: Sentence): Promise<Sentence> {\r\n    return this.save(sentence);\r\n  }\r\n \r\n  async delete(id: string): Promise<void> {\r\n    await this.prisma.sentence.delete({\r\n      where: { id },\r\n    });\r\n  }\r\n \r\n  async findHighestOrder(projectId: string): Promise<number> {\r\n    const result = await this.prisma.sentence.aggregate({\r\n      where: { projectId },\r\n      _max: {\r\n        order: true,\r\n      },\r\n    });\r\n    // If no sentences exist, _max.order will be null, so return 0\r\n    return result._max.order ?? 0;\r\n  }\r\n \r\n  mapPrismaSentenceToDomain(sentence: PrismaSentenceWithRelation): Sentence {\r\n    return new Sentence(\r\n      sentence.id,\r\n      sentence.text,\r\n      sentence.order,\r\n      sentence.status,\r\n      sentence.projectId,\r\n      sentence.createdAt,\r\n      sentence.updatedAt,\r\n      sentence.tone || undefined,\r\n      sentence.emotion || undefined,\r\n      sentence.audioUrl || undefined,\r\n      sentence.audioDuration || undefined,\r\n      sentence.voiceGenerationQueueTasks?.map(\r\n        (vg: any) =>\r\n          new VoiceGeneration(\r\n            vg.id,\r\n            vg.audioUrl,\r\n            vg.audioDuration || undefined,\r\n            vg.status,\r\n            vg.createdAt,\r\n            vg.sentenceId,\r\n            vg.error,\r\n            vg.userId,\r\n            vg.startedAt,\r\n            vg.completedAt,\r\n            vg.totalTokenCount,\r\n            vg.tokenType,\r\n            vg.model,\r\n            vg.updatedAt || undefined,\r\n            vg.generateConfig as any, // Pass generateConfig to VoiceGeneration constructor\r\n          ),\r\n      ),\r\n      sentence.selectedVoiceGenerationId || undefined,\r\n      sentence.selectedVoiceGeneration\r\n        ? new VoiceGeneration(\r\n            sentence.selectedVoiceGeneration.id,\r\n            sentence.selectedVoiceGeneration.audioUrl,\r\n            sentence.selectedVoiceGeneration.audioDuration || undefined,\r\n            sentence.selectedVoiceGeneration.status,\r\n            sentence.selectedVoiceGeneration.createdAt,\r\n            sentence.selectedVoiceGeneration.sentenceId,\r\n            sentence.selectedVoiceGeneration.error,\r\n            sentence.selectedVoiceGeneration.userId,\r\n            sentence.selectedVoiceGeneration.startedAt,\r\n            sentence.selectedVoiceGeneration.completedAt,\r\n            sentence.selectedVoiceGeneration.totalTokenCount,\r\n            sentence.selectedVoiceGeneration.tokenType,\r\n            sentence.selectedVoiceGeneration.model,\r\n            sentence.selectedVoiceGeneration.updatedAt || undefined,\r\n            sentence.selectedVoiceGeneration.generateConfig as any, // Pass generateConfig to VoiceGeneration constructor\r\n          )\r\n        : undefined,\r\n      sentence.generateConfig as any, // Pass generateConfig to Sentence constructor\r\n    );\r\n  }\r\n \r\n}\r\nfunction randomSeed(): number {\r\n  return Math.floor(Math.random() * 10000000);\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;;;AAIO,MAAM;;IACX,YAAY,AAAQ,MAAoB,CAAE;aAAtB,SAAA;IAAuB;IAE3C,MAAM,OAAO,IAAY,EAAE,SAAiB,EAAqB;QAC/D,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO;YACxD,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,SAAS,CAAC;gBAC7C,OAAO;oBAAE;gBAAU;gBACnB,MAAM;oBACJ,OAAO;gBACT;YACF;YACA,MAAM,WAAW,CAAC,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;YAE5C,OAAO,OAAO,QAAQ,CAAC,MAAM,CAAC;gBAC5B,MAAM;oBACJ,IAAI,CAAA,GAAA,8NAAA,CAAA,KAAM,AAAD;oBACT,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,WAAW;oBACX,gBAAgB;wBAAE,WAAW;wBAAQ,MAAM;wBAAc,OAAO;oBAA+B;gBACjG;gBACA,SAAS;oBACP,2BAA2B;oBAC3B,yBAAyB;gBAC3B;YACF;QACF;QAEA,OAAO,IAAI,CAAC,yBAAyB,CAAC;IACxC;IAGA,MAAM,SAAS,EAAU,EAA4B;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACrD,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,2BAA2B;gBAC3B,yBAAyB;YAC3B;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,OAAO,IAAI,CAAC,yBAAyB,CAAC;IACxC;IAEA,MAAM,KAAK,QAAkB,EAAqB;QAChD,MAAM,gBAAgB,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtD,OAAO;gBAAE,IAAI,SAAS,EAAE;YAAC;YACzB,QAAQ;gBACN,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,OAAO;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa;gBACrC,QAAQ,SAAS,MAAM;gBACvB,WAAW,SAAS,SAAS;gBAC7B,2BAA2B,SAAS,yBAAyB;gBAC7D,WAAW,IAAI;gBACf,gBAAgB,SAAS,cAAc;YACzC;YACA,QAAQ;gBACN,IAAI,SAAS,EAAE;gBACf,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,OAAO;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa;gBACrC,QAAQ,SAAS,MAAM;gBACvB,WAAW,SAAS,SAAS;gBAC7B,2BAA2B,SAAS,yBAAyB;gBAC7D,gBAAgB,SAAS,cAAc;YACzC;YACA,SAAS;gBACP,2BAA2B;gBAC3B,yBAAyB;YAC3B;QACF;QAEA,OAAO,IAAI,CAAC,yBAAyB,CAAC;IACxC;IAEA,MAAM,OAAO,QAAkB,EAAqB;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB;IAEA,MAAM,OAAO,EAAU,EAAiB;QACtC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAChC,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAmB;QACzD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAClD,OAAO;gBAAE;YAAU;YACnB,MAAM;gBACJ,OAAO;YACT;QACF;QACA,8DAA8D;QAC9D,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;IAC9B;IAEA,0BAA0B,QAAoC,EAAY;QACxE,OAAO,IAAI,mJAAA,CAAA,WAAQ,CACjB,SAAS,EAAE,EACX,SAAS,IAAI,EACb,SAAS,KAAK,EACd,SAAS,MAAM,EACf,SAAS,SAAS,EAClB,SAAS,SAAS,EAClB,SAAS,SAAS,EAClB,SAAS,IAAI,IAAI,WACjB,SAAS,OAAO,IAAI,WACpB,SAAS,QAAQ,IAAI,WACrB,SAAS,aAAa,IAAI,WAC1B,SAAS,yBAAyB,EAAE,IAClC,CAAC,KACC,IAAI,iKAAA,CAAA,kBAAe,CACjB,GAAG,EAAE,EACL,GAAG,QAAQ,EACX,GAAG,aAAa,IAAI,WACpB,GAAG,MAAM,EACT,GAAG,SAAS,EACZ,GAAG,UAAU,EACb,GAAG,KAAK,EACR,GAAG,MAAM,EACT,GAAG,SAAS,EACZ,GAAG,WAAW,EACd,GAAG,eAAe,EAClB,GAAG,SAAS,EACZ,GAAG,KAAK,EACR,GAAG,SAAS,IAAI,WAChB,GAAG,cAAc,IAGvB,SAAS,yBAAyB,IAAI,WACtC,SAAS,uBAAuB,GAC5B,IAAI,iKAAA,CAAA,kBAAe,CACjB,SAAS,uBAAuB,CAAC,EAAE,EACnC,SAAS,uBAAuB,CAAC,QAAQ,EACzC,SAAS,uBAAuB,CAAC,aAAa,IAAI,WAClD,SAAS,uBAAuB,CAAC,MAAM,EACvC,SAAS,uBAAuB,CAAC,SAAS,EAC1C,SAAS,uBAAuB,CAAC,UAAU,EAC3C,SAAS,uBAAuB,CAAC,KAAK,EACtC,SAAS,uBAAuB,CAAC,MAAM,EACvC,SAAS,uBAAuB,CAAC,SAAS,EAC1C,SAAS,uBAAuB,CAAC,WAAW,EAC5C,SAAS,uBAAuB,CAAC,eAAe,EAChD,SAAS,uBAAuB,CAAC,SAAS,EAC1C,SAAS,uBAAuB,CAAC,KAAK,EACtC,SAAS,uBAAuB,CAAC,SAAS,IAAI,WAC9C,SAAS,uBAAuB,CAAC,cAAc,IAEjD,WACJ,SAAS,cAAc;IAE3B;AAEF;AACA,SAAS;IACP,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;AACpC", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/project/use-cases/CreateProject.ts"], "sourcesContent": ["import type { IProjectRepository } from \"~/domain/project/IProjectRepository\";\r\nimport { Project } from \"~/domain/project/entities/Project\";\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport type { ISentenceRepository } from \"~/domain/sentence/ISentenceRepository\";\r\n\r\ninterface CreateProjectRequest {\r\n  name: string;\r\n  description?: string | null;\r\n  script?: string;\r\n  userId: string;\r\n}\r\n\r\nexport class CreateProject {\r\n  constructor(private projectRepository: IProjectRepository, private sentenceRepository: ISentenceRepository) {}\r\n\r\n  async execute(request: CreateProjectRequest): Promise<Project> {\r\n    const project = Project.create(\r\n      uuidv4(),\r\n      new Date(),\r\n      new Date(),\r\n      {\r\n        name: request.name,\r\n        description: request.description,\r\n        userId: request.userId,\r\n      },\r\n    );\r\n\r\n    try {\r\n      await this.projectRepository.save(project);\r\n      if(request.script) {\r\n        await this.sentenceRepository.create(request.script, project.id);\r\n      }\r\n      return project;\r\n    } catch (error) {\r\n      // Handle the error, e.g., log it, throw a custom application error, etc.\r\n      console.error(\"Failed to save project:\", error);\r\n      throw new Error(\"Could not create project.\"); // Example error handling\r\n    }\r\n  }\r\n}"], "names": [], "mappings": ";;;AACA;AACA;;;AAUO,MAAM;;;IACX,YAAY,AAAQ,iBAAqC,EAAE,AAAQ,kBAAuC,CAAE;aAAxF,oBAAA;aAA+C,qBAAA;IAA0C;IAE7G,MAAM,QAAQ,OAA6B,EAAoB;QAC7D,MAAM,UAAU,iJAAA,CAAA,UAAO,CAAC,MAAM,CAC5B,CAAA,GAAA,8NAAA,CAAA,KAAM,AAAD,KACL,IAAI,QACJ,IAAI,QACJ;YACE,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,QAAQ,QAAQ,MAAM;QACxB;QAGF,IAAI;YACF,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAClC,IAAG,QAAQ,MAAM,EAAE;gBACjB,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,MAAM,EAAE,QAAQ,EAAE;YACjE;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,yEAAyE;YACzE,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM,8BAA8B,yBAAyB;QACzE;IACF;AACF", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/project/use-cases/DeleteProject.ts"], "sourcesContent": ["import type { IProjectRepository } from \"~/domain/project/IProjectRepository\";\r\n\r\nexport class DeleteProject {\r\n  constructor(private projectRepository: IProjectRepository) {}\r\n\r\n  async execute(projectId: string): Promise<void> {\r\n    await this.projectRepository.delete(projectId);\r\n  }\r\n}"], "names": [], "mappings": ";;;AAEO,MAAM;;IACX,YAAY,AAAQ,iBAAqC,CAAE;aAAvC,oBAAA;IAAwC;IAE5D,MAAM,QAAQ,SAAiB,EAAiB;QAC9C,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;IACtC;AACF", "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/project/use-cases/UpdateProject.ts"], "sourcesContent": ["import type { IProjectRepository } from \"~/domain/project/IProjectRepository\";\r\nimport type { Project } from \"~/domain/project/entities/Project\";\r\n\r\ninterface UpdateProjectParams {\r\n  projectId: string;\r\n  name?: string;\r\n  description?: string;\r\n}\r\n\r\nexport class UpdateProject {\r\n  constructor(private projectRepository: IProjectRepository) {}\r\n\r\n  async execute(params: UpdateProjectParams): Promise<Project> {\r\n    const existingProject = await this.projectRepository.getById(params.projectId);\r\n\r\n    if (!existingProject) {\r\n      throw new Error(\"Project not found.\");\r\n    }\r\n\r\n    // In a more robust domain model, Project might have methods like `rename(name: string)`\r\n    if (params.name !== undefined) {\r\n      existingProject.name = params.name;\r\n    }\r\n    \r\n    if (params.description !== undefined) {\r\n      existingProject.updateDescription(params.description);\r\n    }\r\n\r\n    await this.projectRepository.save(existingProject);\r\n    return existingProject;\r\n  }\r\n}"], "names": [], "mappings": ";;;AASO,MAAM;;IACX,YAAY,AAAQ,iBAAqC,CAAE;aAAvC,oBAAA;IAAwC;IAE5D,MAAM,QAAQ,MAA2B,EAAoB;QAC3D,MAAM,kBAAkB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,SAAS;QAE7E,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,wFAAwF;QACxF,IAAI,OAAO,IAAI,KAAK,WAAW;YAC7B,gBAAgB,IAAI,GAAG,OAAO,IAAI;QACpC;QAEA,IAAI,OAAO,WAAW,KAAK,WAAW;YACpC,gBAAgB,iBAAiB,CAAC,OAAO,WAAW;QACtD;QAEA,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAClC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/sentence/use-cases/CreateSentence.ts"], "sourcesContent": ["import type { ISentenceRepository } from \"~/domain/sentence/ISentenceRepository\";\r\nimport type { Sentence } from \"~/domain/sentence/entities/Sentence\";\r\n\r\ninterface CreateSentenceInput {\r\n  projectId: string;\r\n  text: string;\r\n}\r\n\r\nexport class CreateSentence {\r\n  constructor(private sentenceRepository: ISentenceRepository) {}\r\n\r\n  async execute(input: CreateSentenceInput): Promise<Sentence> {\r\n    // Use the repository's create method to handle atomic order calculation and creation\r\n    const newSentence = await this.sentenceRepository.create(input.text, input.projectId);\r\n\r\n    return newSentence;\r\n  }\r\n}"], "names": [], "mappings": ";;;AAQO,MAAM;;IACX,YAAY,AAAQ,kBAAuC,CAAE;aAAzC,qBAAA;IAA0C;IAE9D,MAAM,QAAQ,KAA0B,EAAqB;QAC3D,qFAAqF;QACrF,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,MAAM,SAAS;QAEpF,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/speech/entities/Speech.ts"], "sourcesContent": ["import { BaseEntity } from '../../shared/BaseEntity';\r\nimport { v4 as uuidv4 } from 'uuid';\r\ninterface SpeechProps {\r\n  userId: string;\r\n  text: string;\r\n  audioUrl: string;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n}\r\n\r\nexport class Speech extends BaseEntity {\r\n  userId: string;\r\n  text: string;\r\n  audioUrl: string;\r\n\r\n  private constructor(\r\n    id: string,\r\n    userId: string,\r\n    text: string,\r\n    audioUrl: string,\r\n    createdAt: Date,\r\n    updatedAt: Date,\r\n  ) {\r\n    super(id, createdAt, updatedAt);\r\n    this.userId = userId;\r\n    this.text = text;\r\n    this.audioUrl = audioUrl;\r\n  }\r\n\r\n  public static create(props: SpeechProps, id?: string, createdAt?: Date, updatedAt?: Date): Speech {\r\n  \r\n    const newCreatedAt = createdAt ?? new Date();\r\n    const newUpdatedAt = updatedAt ?? new Date();\r\n    return new  Speech(\r\n      uuidv4(),\r\n      props.userId,\r\n      props.text,\r\n      props.audioUrl,\r\n      newCreatedAt,\r\n      newUpdatedAt,\r\n    );\r\n  }\r\n\r\n  public updateText(text: string): void {\r\n    this.text = text;\r\n    this.updatedAt = new Date();\r\n  }\r\n\r\n  public updateAudioUrl(audioUrl: string): void {\r\n    this.audioUrl = audioUrl;\r\n    this.updatedAt = new Date();\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,MAAM,eAAe,uIAAA,CAAA,aAAU;IACpC,OAAe;IACf,KAAa;IACb,SAAiB;IAEjB,YACE,EAAU,EACV,MAAc,EACd,IAAY,EACZ,QAAgB,EAChB,SAAe,EACf,SAAe,CACf;QACA,KAAK,CAAC,IAAI,WAAW;QACrB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,OAAc,OAAO,KAAkB,EAAE,EAAW,EAAE,SAAgB,EAAE,SAAgB,EAAU;QAEhG,MAAM,eAAe,aAAa,IAAI;QACtC,MAAM,eAAe,aAAa,IAAI;QACtC,OAAO,IAAK,OACV,CAAA,GAAA,8NAAA,CAAA,KAAM,AAAD,KACL,MAAM,MAAM,EACZ,MAAM,IAAI,EACV,MAAM,QAAQ,EACd,cACA;IAEJ;IAEO,WAAW,IAAY,EAAQ;QACpC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;IAEO,eAAe,QAAgB,EAAQ;QAC5C,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI;IACvB;AACF", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/persistence/repositories/PrismaSpeechRepository.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\r\nimport type { ISpeechRepository } from '../../../domain/speech/repositories/ISpeechRepository';\r\nimport { Speech } from '../../../domain/speech/entities/Speech';\r\n\r\nexport class PrismaSpeechRepository implements ISpeechRepository {\r\n  constructor(private prisma: PrismaClient) {}\r\n\r\n  async findById(id: string): Promise<Speech | null> {\r\n    const speechData = await this.prisma.post.findUnique({ // Assuming 'Post' model is used for Speech\r\n      where: { id: parseInt(id) }, // Assuming 'id' is an Int in Prisma for Post\r\n    });\r\n    if (!speechData) {\r\n      return null;\r\n    }\r\n    return Speech.create(\r\n      {\r\n        userId: speechData.createdById,\r\n        text: speechData.name, // Assuming 'name' field in Post is 'text' for Speech\r\n        audioUrl: '', // No direct audioUrl in Post model, needs to be handled\r\n        createdAt: speechData.createdAt,\r\n        updatedAt: speechData.updatedAt,\r\n      },\r\n      speechData.id.toString(), // Convert Int id to string for BaseEntity\r\n    );\r\n  }\r\n\r\n  async findByUserId(userId: string): Promise<Speech[]> {\r\n    const speechDataList = await this.prisma.post.findMany({\r\n      where: { createdById: userId },\r\n    });\r\n\r\n    return speechDataList.map((speechData) =>\r\n      Speech.create(\r\n        {\r\n          userId: speechData.createdById,\r\n          text: speechData.name,\r\n          audioUrl: '', // No direct audioUrl in Post model, needs to be handled\r\n          createdAt: speechData.createdAt,\r\n          updatedAt: speechData.updatedAt,\r\n        },\r\n        speechData.id.toString(),\r\n      ),\r\n    );\r\n  }\r\n\r\n  async save(speech: Speech): Promise<void> {\r\n    await this.prisma.post.upsert({\r\n      where: { id: parseInt(speech.id) }, // Convert string id to Int for Prisma\r\n      update: {\r\n        name: speech.text,\r\n        createdById: speech.userId,\r\n        updatedAt: speech.updatedAt,\r\n      },\r\n      create: {\r\n        name: speech.text,\r\n        createdById: speech.userId,\r\n        createdAt: speech.createdAt,\r\n        updatedAt: speech.updatedAt,\r\n      },\r\n    });\r\n  }\r\n\r\n  async delete(id: string): Promise<void> {\r\n    await this.prisma.post.delete({\r\n      where: { id: parseInt(id) },\r\n    });\r\n  }\r\n}"], "names": [], "mappings": ";;;AAEA;;AAEO,MAAM;;IACX,YAAY,AAAQ,MAAoB,CAAE;aAAtB,SAAA;IAAuB;IAE3C,MAAM,SAAS,EAAU,EAA0B;QACjD,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI,SAAS;YAAI;QAC5B;QACA,IAAI,CAAC,YAAY;YACf,OAAO;QACT;QACA,OAAO,+IAAA,CAAA,SAAM,CAAC,MAAM,CAClB;YACE,QAAQ,WAAW,WAAW;YAC9B,MAAM,WAAW,IAAI;YACrB,UAAU;YACV,WAAW,WAAW,SAAS;YAC/B,WAAW,WAAW,SAAS;QACjC,GACA,WAAW,EAAE,CAAC,QAAQ;IAE1B;IAEA,MAAM,aAAa,MAAc,EAAqB;QACpD,MAAM,iBAAiB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACrD,OAAO;gBAAE,aAAa;YAAO;QAC/B;QAEA,OAAO,eAAe,GAAG,CAAC,CAAC,aACzB,+IAAA,CAAA,SAAM,CAAC,MAAM,CACX;gBACE,QAAQ,WAAW,WAAW;gBAC9B,MAAM,WAAW,IAAI;gBACrB,UAAU;gBACV,WAAW,WAAW,SAAS;gBAC/B,WAAW,WAAW,SAAS;YACjC,GACA,WAAW,EAAE,CAAC,QAAQ;IAG5B;IAEA,MAAM,KAAK,MAAc,EAAiB;QACxC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE,IAAI,SAAS,OAAO,EAAE;YAAE;YACjC,QAAQ;gBACN,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,MAAM;gBAC1B,WAAW,OAAO,SAAS;YAC7B;YACA,QAAQ;gBACN,MAAM,OAAO,IAAI;gBACjB,aAAa,OAAO,MAAM;gBAC1B,WAAW,OAAO,SAAS;gBAC3B,WAAW,OAAO,SAAS;YAC7B;QACF;IACF;IAEA,MAAM,OAAO,EAAU,EAAiB;QACtC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE,IAAI,SAAS;YAAI;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/speech/use-cases/CreateSpeech.ts"], "sourcesContent": ["import type { ISpeechRepository } from '../../../domain/speech/repositories/ISpeechRepository';\r\nimport { Speech } from '../../../domain/speech/entities/Speech';\r\nimport type { CreateSpeechDto } from '../dtos/CreateSpeechDto';\r\nimport type { SpeechResponseDto } from '../dtos/SpeechResponseDto';\r\n\r\nexport class CreateSpeech {\r\n  constructor(private speechRepository: ISpeechRepository) {}\r\n\r\n  public async execute(input: CreateSpeechDto): Promise<SpeechResponseDto> {\r\n    const speech = Speech.create({\r\n      userId: input.userId,\r\n      text: input.text,\r\n      audioUrl: input.audioUrl,\r\n    });\r\n\r\n    await this.speechRepository.save(speech);\r\n\r\n    return {\r\n      id: speech.id,\r\n      userId: speech.userId,\r\n      text: speech.text,\r\n      audioUrl: speech.audioUrl,\r\n      createdAt: speech.createdAt,\r\n      updatedAt: speech.updatedAt,\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;AACA;;AAIO,MAAM;;IACX,YAAY,AAAQ,gBAAmC,CAAE;aAArC,mBAAA;IAAsC;IAE1D,MAAa,QAAQ,KAAsB,EAA8B;QACvE,MAAM,SAAS,+IAAA,CAAA,SAAM,CAAC,MAAM,CAAC;YAC3B,QAAQ,MAAM,MAAM;YACpB,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;QAC1B;QAEA,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAEjC,OAAO;YACL,IAAI,OAAO,EAAE;YACb,QAAQ,OAAO,MAAM;YACrB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,WAAW,OAAO,SAAS;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/utils/createQueuedFunction.ts"], "sourcesContent": ["type AsyncFunction<T extends unknown[], R> = (...args: T) => Promise<R>;\r\n\r\nexport function createQueuedFunction<T extends unknown[], R>(\r\n  func: AsyncFunction<T, R>\r\n): AsyncFunction<T, R> {\r\n  const queue: Array<{ args: T; resolve: (value: R | PromiseLike<R>) => void; reject: (reason?: unknown) => void }> = [];\r\n  let isProcessing = false;\r\n\r\n  const processNext = async (): Promise<void> => {\r\n    if (queue.length === 0) {\r\n      isProcessing = false;\r\n      return;\r\n    }\r\n\r\n    isProcessing = true;\r\n    const { args, resolve, reject } = queue.shift()!; // Get the next task\r\n\r\n    try {\r\n      const result = await func(...args);\r\n      resolve(result);\r\n    } catch (error) {\r\n      reject(error);\r\n    } finally {\r\n      // Process the next task regardless of success or failure\r\n      processNext().catch((err) => {\r\n        console.error(\"Error processing queue:\", err);\r\n      });\r\n    }\r\n  };\r\n\r\n  return (...args: T): Promise<R> => {\r\n    return new Promise<R>((resolve, reject) => {\r\n      queue.push({ args, resolve, reject });\r\n      if (!isProcessing) {\r\n        processNext().catch((err) => {\r\n          console.error(\"Error starting queue processing:\", err);\r\n        });\r\n      }\r\n    });\r\n  };\r\n}"], "names": [], "mappings": ";;;AAEO,SAAS,qBACd,IAAyB;IAEzB,MAAM,QAA8G,EAAE;IACtH,IAAI,eAAe;IAEnB,MAAM,cAAc;QAClB,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,eAAe;YACf;QACF;QAEA,eAAe;QACf,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,KAAK,IAAK,oBAAoB;QAEtE,IAAI;YACF,MAAM,SAAS,MAAM,QAAQ;YAC7B,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,OAAO;QACT,SAAU;YACR,yDAAyD;YACzD,cAAc,KAAK,CAAC,CAAC;gBACnB,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,OAAO,CAAC,GAAG;QACT,OAAO,IAAI,QAAW,CAAC,SAAS;YAC9B,MAAM,IAAI,CAAC;gBAAE;gBAAM;gBAAS;YAAO;YACnC,IAAI,CAAC,cAAc;gBACjB,cAAc,KAAK,CAAC,CAAC;oBACnB,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/queue/InAppQueueService.ts"], "sourcesContent": ["import type { PrismaClient, VoiceGenerationQueue } from \"@prisma/client\";\r\nimport { VoiceGenerationQueueProcessor } from \"./VoiceGenerationQueueProcessor\";\r\nimport { db } from \"../persistence/prisma/client\";\r\nimport { createQueuedFunction } from \"../../utils/createQueuedFunction\";\r\n\r\nexport class InAppQueueService {\r\n  private processingPromise: Promise<void> | null = null;\r\n  private queuedAddTask: (sentenceId: string, userId: string) => Promise<VoiceGenerationQueue>;\r\n\r\n  constructor(private prisma: PrismaClient, private processor: VoiceGenerationQueueProcessor) {\r\n    // Bind the processor's addTask method to its instance before queuing it\r\n    const boundAddTask = this.processor.addTask.bind(this.processor);\r\n\r\n    // Decorate the processor's addTask method with the queuing logic\r\n    this.queuedAddTask = createQueuedFunction(boundAddTask);\r\n  }\r\n\r\n  async start(): Promise<void> {\r\n    await this.processQueue();\r\n  }\r\n\r\n  async processQueue(): Promise<void> {\r\n    if (this.processingPromise) {\r\n      // If already processing, wait for the current process to finish\r\n      return this.processingPromise;\r\n    }\r\n\r\n    // Start processing and store the promise\r\n    this.processingPromise = this.processor.processQueue().finally(() => {\r\n      // Reset the promise after processing is done (success or failure)\r\n      this.processingPromise = null;\r\n    });\r\n\r\n    // Return the promise so callers can await the processing completion\r\n    return this.processingPromise;\r\n  }\r\n  \r\n  async addTask(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {\r\n    return this.queuedAddTask(sentenceId, userId);\r\n  }\r\n}\r\n\r\n// export const queueProcessor = new InAppQueueService(db, new VoiceGenerationQueueProcessor(db));"], "names": [], "mappings": ";;;AAGA;;AAEO,MAAM;;;IACH,kBAA+C;IAC/C,cAAqF;IAE7F,YAAY,AAAQ,MAAoB,EAAE,AAAQ,SAAwC,CAAE;aAAxE,SAAA;aAA8B,YAAA;aAH1C,oBAA0C;QAIhD,wEAAwE;QACxE,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;QAE/D,iEAAiE;QACjE,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EAAE;IAC5C;IAEA,MAAM,QAAuB;QAC3B,MAAM,IAAI,CAAC,YAAY;IACzB;IAEA,MAAM,eAA8B;QAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,gEAAgE;YAChE,OAAO,IAAI,CAAC,iBAAiB;QAC/B;QAEA,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,OAAO,CAAC;YAC7D,kEAAkE;YAClE,IAAI,CAAC,iBAAiB,GAAG;QAC3B;QAEA,oEAAoE;QACpE,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA,MAAM,QAAQ,UAAkB,EAAE,MAAc,EAAiC;QAC/E,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY;IACxC;AACF,EAEA,kGAAkG", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/utils/pubsub.ts"], "sourcesContent": ["import EventEmitter from \"events\";\r\nimport { type VoiceGenerationQueue } from \"@prisma/client\";\r\nimport { Sentence } from \"~/domain/sentence/entities/Sentence\";\r\n\r\nexport interface PubSubEvents {\r\n  'queueTaskUpdate': VoiceGenerationQueue;\r\n  'sentenceUpdate': { projectId: string; sentence: Sentence; type: 'create' | 'update' | 'delete' };\r\n}\r\n\r\nexport const ee = new EventEmitter();"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM,KAAK,IAAI,qGAAA,CAAA,UAAY", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/tts/GoogleTTSGenerationService.ts"], "sourcesContent": ["import * as fs from 'fs';\r\nimport { GoogleGenAI, type GenerateContentConfig } from '@google/genai';\r\nimport { TokenType } from '@prisma/client';\r\nimport { type ITTSGenerationService, type TTSGenerationConfig } from '~/domain/tts/ITTSGenerationService';\r\n\r\nexport class GoogleTTSGenerationService implements ITTSGenerationService {\r\n  private ai: GoogleGenAI;\r\n\r\n  constructor() {\r\n    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;\r\n    if (!apiKey) {\r\n      throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is not set in environment variables.');\r\n    }\r\n    this.ai = new GoogleGenAI({ apiKey });\r\n  }\r\n  async calTokenCount(text: string): Promise<number> {\r\n    const countResult = await this.ai.models.countTokens({\r\n      model: 'gemini-2.5-flash-preview-tts',\r\n      contents: text,\r\n      config: {\r\n        \r\n      },\r\n    });\r\n    return (countResult.totalTokens ?? 0) * 5;\r\n  }\r\n\r\n  async generateVoice(text: string, config?: TTSGenerationConfig){\r\n    const response = await this.ai.models.generateContent({\r\n      model: config?.model || 'gemini-2.5-flash-preview-tts',\r\n      contents: [{ parts: [{ text }] }],\r\n      config: {\r\n        responseModalities: ['AUDIO'],\r\n        seed: config?.seed,\r\n        speechConfig: {\r\n          voiceConfig: {\r\n            prebuiltVoiceConfig: {\r\n              voiceName: config?.voiceName ?? 'Kore', // Use provided voiceName or default to 'Kore'\r\n            },\r\n          },\r\n          languageCode: 'vi-VN',\r\n        },\r\n      },\r\n    });\r\n\r\n    const data = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;\r\n    const totalTokenCount = response.usageMetadata?.totalTokenCount;\r\n\r\n    if (!data) {\r\n      throw new Error('No audio content received from Gemini API.');\r\n    }\r\n\r\n    return {\r\n      buffer: Buffer.from(data, 'base64'),\r\n      totalTokenCount: totalTokenCount || 0,\r\n      tokenType: TokenType.GOOGLE_GENERATIVE_AI,\r\n      model: 'gemini-2.5-flash-preview-tts'\r\n    };\r\n  }\r\n\r\n  async generateVoiceStream(text: string, config?: TTSGenerationConfig) {\r\n    const response = await this.ai.models.generateContentStream({\r\n      model: config?.model || 'gemini-2.5-flash-preview-tts',\r\n      contents: [{ parts: [{ text }] }],\r\n      config: {\r\n        responseModalities: ['AUDIO'],\r\n        seed: config?.seed,\r\n        speechConfig: {\r\n          voiceConfig: {\r\n            prebuiltVoiceConfig: {\r\n              voiceName: config?.voiceName || 'Kore', // Use provided voiceName or default to 'Kore'\r\n            },\r\n          },\r\n          languageCode: 'vi-VN',\r\n        },\r\n      },\r\n    });\r\n    const allChunks: any[] = [];\r\n    for await (const chunk of response) {\r\n      allChunks.push(chunk);\r\n      console.log(chunk);\r\n    }\r\n    await fs.promises.writeFile('tts_response.json', JSON.stringify(allChunks, null, 2));\r\n    return response;\r\n  }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,MAAM;IACH,GAAgB;IAExB,aAAc;QACZ,MAAM,SAAS,QAAQ,GAAG,CAAC,4BAA4B;QACvD,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,EAAE,GAAG,IAAI,2QAAA,CAAA,cAAW,CAAC;YAAE;QAAO;IACrC;IACA,MAAM,cAAc,IAAY,EAAmB;QACjD,MAAM,cAAc,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC;YACnD,OAAO;YACP,UAAU;YACV,QAAQ,CAER;QACF;QACA,OAAO,CAAC,YAAY,WAAW,IAAI,CAAC,IAAI;IAC1C;IAEA,MAAM,cAAc,IAAY,EAAE,MAA4B,EAAC;QAC7D,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC;YACpD,OAAO,QAAQ,SAAS;YACxB,UAAU;gBAAC;oBAAE,OAAO;wBAAC;4BAAE;wBAAK;qBAAE;gBAAC;aAAE;YACjC,QAAQ;gBACN,oBAAoB;oBAAC;iBAAQ;gBAC7B,MAAM,QAAQ;gBACd,cAAc;oBACZ,aAAa;wBACX,qBAAqB;4BACnB,WAAW,QAAQ,aAAa;wBAClC;oBACF;oBACA,cAAc;gBAChB;YACF;QACF;QAEA,MAAM,OAAO,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,YAAY;QACxE,MAAM,kBAAkB,SAAS,aAAa,EAAE;QAEhD,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,QAAQ,OAAO,IAAI,CAAC,MAAM;YAC1B,iBAAiB,mBAAmB;YACpC,WAAW,6HAAA,CAAA,YAAS,CAAC,oBAAoB;YACzC,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,IAAY,EAAE,MAA4B,EAAE;QACpE,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,qBAAqB,CAAC;YAC1D,OAAO,QAAQ,SAAS;YACxB,UAAU;gBAAC;oBAAE,OAAO;wBAAC;4BAAE;wBAAK;qBAAE;gBAAC;aAAE;YACjC,QAAQ;gBACN,oBAAoB;oBAAC;iBAAQ;gBAC7B,MAAM,QAAQ;gBACd,cAAc;oBACZ,aAAa;wBACX,qBAAqB;4BACnB,WAAW,QAAQ,aAAa;wBAClC;oBACF;oBACA,cAAc;gBAChB;YACF;QACF;QACA,MAAM,YAAmB,EAAE;QAC3B,WAAW,MAAM,SAAS,SAAU;YAClC,UAAU,IAAI,CAAC;YACf,QAAQ,GAAG,CAAC;QACd;QACA,MAAM,6FAAA,CAAA,WAAW,CAAC,SAAS,CAAC,qBAAqB,KAAK,SAAS,CAAC,WAAW,MAAM;QACjF,OAAO;IACT;AAEF", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/queue/VoiceGenerationQueueProcessor.ts"], "sourcesContent": ["import { PrismaClient, TokenType, VoiceGenerationStatus, type VoiceGenerationQueue } from \"@prisma/client\";\r\nimport { ee } from \"~/utils/pubsub\"; // Import the pub/sub emitter\r\nimport { GoogleTTSGenerationService } from \"~/infrastructure/tts/GoogleTTSGenerationService\";\r\nimport { type ITTSGenerationService, type TTSGenerationConfig } from \"~/domain/tts/ITTSGenerationService\";\r\nimport * as path from 'node:path';\r\nimport wav from 'wav';\r\nimport { createQueuedFunction } from \"~/utils/createQueuedFunction\";\r\nconst MAX_CONCURRENT_TASKS = 5; // System-wide concurrency limit\r\nconst MAX_CONCURRENT_TASKS_PER_USER = 1; // Per-user concurrency limit\r\nconst PROCESS_QUEUE_TIMEOUT = 150000;\r\n\r\nfunction sleep(ms: number) {\r\n  return new Promise(resolve => setTimeout(resolve, ms));\r\n}\r\n\r\nasync function saveWaveFile(\r\n   filename: string,\r\n   pcmData: Buffer,\r\n   channels = 1,\r\n   rate = 24000,\r\n   sampleWidth = 2,\r\n) {\r\n   return new Promise((resolve, reject) => {\r\n      const writer = new wav.FileWriter(filename, {\r\n            channels,\r\n            sampleRate: rate,\r\n            bitDepth: sampleWidth * 8,\r\n      });\r\n\r\n      writer.on('finish', resolve);\r\n      writer.on('error', reject);\r\n\r\n      writer.write(pcmData);\r\n      writer.end();\r\n   });\r\n}\r\nexport class VoiceGenerationQueueProcessor {\r\n  private ttsService: ITTSGenerationService;\r\n\r\n  constructor(private prisma: PrismaClient, ttsService?: ITTSGenerationService) {\r\n    this.ttsService = ttsService || new GoogleTTSGenerationService();\r\n    this.promoteWaitingTaskForUser = createQueuedFunction(this.promoteWaitingTaskForUser.bind(this));\r\n  }\r\n\r\n  async addTask(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {\r\n    const totalUserPendingTasks = await this.prisma.voiceGenerationQueue.count({\r\n      where: {\r\n        userId: userId,\r\n        status: {\r\n          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING],\r\n        },\r\n      },\r\n    });\r\n    const result =  await this.prisma.voiceGenerationQueue.create({\r\n      data: {\r\n        sentenceId: sentenceId,\r\n        selectedBySentence: { connect: { id: sentenceId } },\r\n        userId: userId,\r\n        status: totalUserPendingTasks < MAX_CONCURRENT_TASKS_PER_USER ? VoiceGenerationStatus.PENDING : VoiceGenerationStatus.WAITING_USER,\r\n      },\r\n    });\r\n    return result;\r\n  }\r\n\r\n  async processTask(task: VoiceGenerationQueue): Promise<void> {\r\n   // Update task status to PROCESSING\r\n      await this.prisma.voiceGenerationQueue.update({\r\n        where: { id: task.id },\r\n        data: {\r\n          status: VoiceGenerationStatus.PROCESSING,\r\n          startedAt: new Date(),\r\n        },\r\n      });\r\n\r\n      // Send real-time update to frontend (status: PROCESSING)\r\n      ee.emit('queueTaskUpdate', task);\r\n\r\n      try {\r\n\r\n       \r\n        const sentence = await this.prisma.sentence.findUnique({\r\n          where: { id: task.sentenceId },\r\n        });\r\n\r\n        if (!sentence) {\r\n          throw new Error(`Sentence with ID ${task.sentenceId} not found.`);\r\n        }\r\n\r\n        const tokenCount = await this.ttsService.calTokenCount(sentence.text)\r\n        \r\n        const userAvailableTokenForTokenType = await this.prisma.userTokenAvailable.findUnique({\r\n          where: { \r\n            userId_tokenType: {\r\n              userId: task.userId,\r\n              tokenType: TokenType.GOOGLE_GENERATIVE_AI,\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!userAvailableTokenForTokenType || userAvailableTokenForTokenType.amount < tokenCount) {\r\n          throw new Error(`Not enough tokens available. Required ${tokenCount}, but only ${userAvailableTokenForTokenType?.amount} available.`);\r\n        }\r\n\r\n        console.log(`Queue processor: Generating voice for sentence ${task.sentenceId} with text: \"${sentence.text}\"`);\r\n        // TODO: Make voiceName configurable, perhaps from task metadata or user preferences\r\n        const voiceName = 'Kore'; // Example Vietnamese voice name\r\n        const { buffer:audioBuffer, totalTokenCount, tokenType, model } = await this.ttsService.generateVoice(sentence.text, sentence.generateConfig as TTSGenerationConfig);\r\n        const audioFileName = `${task.id}.wav`;\r\n        const audioDir = path.join(process.cwd(), 'public', 'generated-audio');\r\n        const audioFilePath = path.join(audioDir, audioFileName);\r\n        await saveWaveFile(audioFilePath, audioBuffer)\r\n    \r\n        const audioUrl = `/generated-audio/${audioFileName}`;\r\n        const audioDuration = 5; // Placeholder duration, ideally calculated from audioBuffer or metadata\r\n\r\n        const {completedTask, updatedUserTokenAvailable} = await this.prisma.$transaction(async tx => {\r\n          // Upon success: Update task status to PROCESSED_SUCCESS\r\n          const completedTask = await tx.voiceGenerationQueue.update({\r\n            where: { id: task.id },\r\n            data: {\r\n              status: VoiceGenerationStatus.COMPLETED, // this update would release available capacity for the user\r\n              completedAt: new Date(),\r\n              audioUrl: audioUrl,\r\n              audioDuration: audioDuration,\r\n              totalTokenCount,\r\n              model,\r\n              tokenType,\r\n              generateConfig: sentence.generateConfig || undefined,\r\n            },\r\n          });\r\n\r\n          // Send real-time update to frontend (status: PROCESSED_SUCCESS, with results)\r\n          // const completedTask = await this.prisma.voiceGenerationQueue.findUniqueOrThrow({ where: { id: task.id } }); // Fetch updated task with results\r\n          console.log(`Queue processor: Task ${task.id} processed successfully.`);\r\n          await tx.userTokenLedger.create({\r\n              data: {\r\n                userId: task.userId,\r\n                tokenType: tokenType as TokenType,\r\n                amount: -totalTokenCount,\r\n                message: `Voice generation for sentence ${sentence.text}`,\r\n                model\r\n              },\r\n          });\r\n          const updatedUserTokenAvailable = await tx.userTokenAvailable.update({\r\n            where: { \r\n              userId_tokenType: {\r\n                userId: task.userId,\r\n                tokenType: tokenType as TokenType,\r\n              },\r\n            },\r\n            data: {\r\n              amount: {\r\n                decrement: totalTokenCount,\r\n              },\r\n            },\r\n          });\r\n          return {completedTask, updatedUserTokenAvailable};\r\n        })\r\n        ee.emit('userTokenAvailableUpdate', updatedUserTokenAvailable);\r\n        ee.emit('queueTaskUpdate', completedTask);\r\n        // Check for and promote waiting tasks for this user\r\n        await this.promoteWaitingTaskForUser(task.userId);\r\n\r\n      } catch (error) {\r\n        // Upon failure: Update task status to FAILED\r\n        console.error(`Queue processor: Task ${task.id} failed:`, error);\r\n        await this.prisma.voiceGenerationQueue.update({\r\n          where: { id: task.id },\r\n          data: {\r\n            status: VoiceGenerationStatus.FAILED,\r\n            completedAt: new Date(),\r\n            error: error instanceof Error ? error.message : 'Unknown error',\r\n          },\r\n        });\r\n        // Send real-time update to frontend (status: FAILED, with error)\r\n        const failedTask = await this.prisma.voiceGenerationQueue.findUniqueOrThrow({ where: { id: task.id } }); // Fetch updated task with error\r\n        ee.emit('queueTaskUpdate', failedTask);\r\n        // Check for and promote waiting tasks for this user even on failure\r\n        await this.promoteWaitingTaskForUser(task.userId);\r\n      }\r\n  }\r\n\r\n\r\n  async processQueue(): Promise<void> {\r\n    // Check current processing tasks\r\n    const processingTasksCount = await this.prisma.voiceGenerationQueue.count({\r\n      where: {\r\n        status: VoiceGenerationStatus.PROCESSING,\r\n      },\r\n    });\r\n\r\n    const availableCapacity = MAX_CONCURRENT_TASKS - processingTasksCount;\r\n\r\n    if (availableCapacity <= 0) {\r\n      console.log(\"Queue processor: Max concurrency reached. Waiting for tasks to complete.\");\r\n      return;\r\n    }\r\n\r\n    // Find pending tasks up to available capacity\r\n    const pendingTasks = await this.prisma.voiceGenerationQueue.findMany({\r\n      where: {\r\n        status: VoiceGenerationStatus.PENDING,\r\n      },\r\n      orderBy: {\r\n        createdAt: 'asc',\r\n      },\r\n      take: availableCapacity,\r\n    });\r\n\r\n    if (pendingTasks.length === 0) {\r\n      console.log(\"Queue processor: No pending tasks.\");\r\n      return;\r\n    }\r\n\r\n    console.log(`Queue processor: Found ${pendingTasks.length} pending tasks. Processing...`);\r\n\r\n    // Process tasks\r\n    for (const task of pendingTasks) {\r\n      this.processTask(task);\r\n    }\r\n    \r\n    const timeoutTasks = await this.prisma.voiceGenerationQueue.findMany({\r\n      where: {\r\n        status: VoiceGenerationStatus.PROCESSING,\r\n        startedAt: {\r\n          lte: new Date(Date.now() - PROCESS_QUEUE_TIMEOUT),\r\n        },\r\n      },\r\n    });\r\n\r\n    if (timeoutTasks.length > 0) {\r\n      console.log(`Queue processor: Found ${timeoutTasks.length} timed out tasks. Marking as failed.`);\r\n      for (const task of timeoutTasks) {\r\n        const failedTask = await this.prisma.voiceGenerationQueue.update({\r\n          where: { id: task.id },\r\n          data: {\r\n            status: VoiceGenerationStatus.FAILED,\r\n            completedAt: new Date(),\r\n            error: 'Task timed out',\r\n          },\r\n        });\r\n        // Send real-time update to frontend (status: FAILED, with error)\r\n        ee.emit('queueTaskUpdate', failedTask);\r\n        this.promoteWaitingTaskForUser(task.userId);\r\n      }\r\n    }\r\n\r\n\r\n  }\r\n\r\n  private async promoteWaitingTaskForUser(userId: string): Promise<void> {\r\n    const waitingTask = await this.prisma.voiceGenerationQueue.findFirst({\r\n      where: {\r\n        userId: userId,\r\n        status: VoiceGenerationStatus.WAITING_USER,\r\n      },\r\n      orderBy: {\r\n        createdAt: 'asc',\r\n      },\r\n    });\r\n\r\n    if (waitingTask) {\r\n      console.log(`Queue processor: Promoting waiting task ${waitingTask.id} for user ${userId}`);\r\n      await this.prisma.voiceGenerationQueue.update({\r\n        where: { id: waitingTask.id },\r\n        data: {\r\n          status: VoiceGenerationStatus.PENDING,\r\n        },\r\n      });\r\n      // Trigger the processor again to pick up the newly pending task\r\n      // TODO: Implement actual trigger mechanism - This will depend on how the processor is run (e.g., message queue, cron job, direct call)\r\n      console.log(\"Queue processor: Triggering processor after promoting task.\");\r\n      return this.processQueue(); // Or send a message queue event\r\n    }\r\n  }\r\n}\r\n\r\n// Example usage (this would typically be run as a separate process or scheduled job)\r\n// const prisma = new PrismaClient();\r\n// const processor = new VoiceGenerationQueueProcessor(prisma);\r\n// processor.processQueue().catch(console.error);"], "names": [], "mappings": ";;;AAAA;AACA,kNAAqC,6BAA6B;AAClE;AAEA;AACA;AACA;;;;;;;AACA,MAAM,uBAAuB,GAAG,gCAAgC;AAChE,MAAM,gCAAgC,GAAG,6BAA6B;AACtE,MAAM,wBAAwB;AAE9B,SAAS,MAAM,EAAU;IACvB,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEA,eAAe,aACZ,QAAgB,EAChB,OAAe,EACf,WAAW,CAAC,EACZ,OAAO,KAAK,EACZ,cAAc,CAAC;IAEf,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC1B,MAAM,SAAS,IAAI,8KAAA,CAAA,UAAG,CAAC,UAAU,CAAC,UAAU;YACtC;YACA,YAAY;YACZ,UAAU,cAAc;QAC9B;QAEA,OAAO,EAAE,CAAC,UAAU;QACpB,OAAO,EAAE,CAAC,SAAS;QAEnB,OAAO,KAAK,CAAC;QACb,OAAO,GAAG;IACb;AACH;AACO,MAAM;;IACH,WAAkC;IAE1C,YAAY,AAAQ,MAAoB,EAAE,UAAkC,CAAE;aAA1D,SAAA;QAClB,IAAI,CAAC,UAAU,GAAG,cAAc,IAAI,4JAAA,CAAA,6BAA0B;QAC9D,IAAI,CAAC,yBAAyB,GAAG,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI;IAChG;IAEA,MAAM,QAAQ,UAAkB,EAAE,MAAc,EAAiC;QAC/E,MAAM,wBAAwB,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;YACzE,OAAO;gBACL,QAAQ;gBACR,QAAQ;oBACN,IAAI;wBAAC,6HAAA,CAAA,wBAAqB,CAAC,OAAO;wBAAE,6HAAA,CAAA,wBAAqB,CAAC,UAAU;qBAAC;gBACvE;YACF;QACF;QACA,MAAM,SAAU,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC5D,MAAM;gBACJ,YAAY;gBACZ,oBAAoB;oBAAE,SAAS;wBAAE,IAAI;oBAAW;gBAAE;gBAClD,QAAQ;gBACR,QAAQ,wBAAwB,gCAAgC,6HAAA,CAAA,wBAAqB,CAAC,OAAO,GAAG,6HAAA,CAAA,wBAAqB,CAAC,YAAY;YACpI;QACF;QACA,OAAO;IACT;IAEA,MAAM,YAAY,IAA0B,EAAiB;QAC5D,mCAAmC;QAChC,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,MAAM;gBACJ,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,UAAU;gBACxC,WAAW,IAAI;YACjB;QACF;QAEA,yDAAyD;QACzD,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,mBAAmB;QAE3B,IAAI;YAGF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACrD,OAAO;oBAAE,IAAI,KAAK,UAAU;gBAAC;YAC/B;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,WAAW,CAAC;YAClE;YAEA,MAAM,aAAa,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,IAAI;YAEpE,MAAM,iCAAiC,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBACrF,OAAO;oBACL,kBAAkB;wBAChB,QAAQ,KAAK,MAAM;wBACnB,WAAW,6HAAA,CAAA,YAAS,CAAC,oBAAoB;oBAC3C;gBACF;YACF;YAEA,IAAI,CAAC,kCAAkC,+BAA+B,MAAM,GAAG,YAAY;gBACzF,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,WAAW,WAAW,EAAE,gCAAgC,OAAO,WAAW,CAAC;YACtI;YAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,KAAK,UAAU,CAAC,aAAa,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;YAC7G,oFAAoF;YACpF,MAAM,YAAY,QAAQ,gCAAgC;YAC1D,MAAM,EAAE,QAAO,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE,SAAS,cAAc;YAC5I,MAAM,gBAAgB,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC;YACtC,MAAM,WAAW,CAAA,GAAA,iHAAA,CAAA,OAAS,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;YACpD,MAAM,gBAAgB,CAAA,GAAA,iHAAA,CAAA,OAAS,AAAD,EAAE,UAAU;YAC1C,MAAM,aAAa,eAAe;YAElC,MAAM,WAAW,CAAC,iBAAiB,EAAE,eAAe;YACpD,MAAM,gBAAgB,GAAG,wEAAwE;YAEjG,MAAM,EAAC,aAAa,EAAE,yBAAyB,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAM;gBACtF,wDAAwD;gBACxD,MAAM,gBAAgB,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC;oBACzD,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBACJ,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,SAAS;wBACvC,aAAa,IAAI;wBACjB,UAAU;wBACV,eAAe;wBACf;wBACA;wBACA;wBACA,gBAAgB,SAAS,cAAc,IAAI;oBAC7C;gBACF;gBAEA,8EAA8E;gBAC9E,iJAAiJ;gBACjJ,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,wBAAwB,CAAC;gBACtE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;oBAC5B,MAAM;wBACJ,QAAQ,KAAK,MAAM;wBACnB,WAAW;wBACX,QAAQ,CAAC;wBACT,SAAS,CAAC,8BAA8B,EAAE,SAAS,IAAI,EAAE;wBACzD;oBACF;gBACJ;gBACA,MAAM,4BAA4B,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC;oBACnE,OAAO;wBACL,kBAAkB;4BAChB,QAAQ,KAAK,MAAM;4BACnB,WAAW;wBACb;oBACF;oBACA,MAAM;wBACJ,QAAQ;4BACN,WAAW;wBACb;oBACF;gBACF;gBACA,OAAO;oBAAC;oBAAe;gBAAyB;YAClD;YACA,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,4BAA4B;YACpC,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,mBAAmB;YAC3B,oDAAoD;YACpD,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,MAAM;QAElD,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC1D,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAC5C,OAAO;oBAAE,IAAI,KAAK,EAAE;gBAAC;gBACrB,MAAM;oBACJ,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,MAAM;oBACpC,aAAa,IAAI;oBACjB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;YACA,iEAAiE;YACjE,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,iBAAiB,CAAC;gBAAE,OAAO;oBAAE,IAAI,KAAK,EAAE;gBAAC;YAAE,IAAI,gCAAgC;YACzI,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,mBAAmB;YAC3B,oEAAoE;YACpE,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,MAAM;QAClD;IACJ;IAGA,MAAM,eAA8B;QAClC,iCAAiC;QACjC,MAAM,uBAAuB,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC;YACxE,OAAO;gBACL,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,UAAU;YAC1C;QACF;QAEA,MAAM,oBAAoB,uBAAuB;QAEjD,IAAI,qBAAqB,GAAG;YAC1B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YACnE,OAAO;gBACL,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,OAAO;YACvC;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;QACR;QAEA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa,MAAM,CAAC,6BAA6B,CAAC;QAExF,gBAAgB;QAChB,KAAK,MAAM,QAAQ,aAAc;YAC/B,IAAI,CAAC,WAAW,CAAC;QACnB;QAEA,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;YACnE,OAAO;gBACL,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,UAAU;gBACxC,WAAW;oBACT,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK;gBAC7B;YACF;QACF;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa,MAAM,CAAC,oCAAoC,CAAC;YAC/F,KAAK,MAAM,QAAQ,aAAc;gBAC/B,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBAC/D,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBACJ,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,MAAM;wBACpC,aAAa,IAAI;wBACjB,OAAO;oBACT;gBACF;gBACA,iEAAiE;gBACjE,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,mBAAmB;gBAC3B,IAAI,CAAC,yBAAyB,CAAC,KAAK,MAAM;YAC5C;QACF;IAGF;IAEA,MAAc,0BAA0B,MAAc,EAAiB;QACrE,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC;YACnE,OAAO;gBACL,QAAQ;gBACR,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,YAAY;YAC5C;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,YAAY,EAAE,CAAC,UAAU,EAAE,QAAQ;YAC1F,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAC5C,OAAO;oBAAE,IAAI,YAAY,EAAE;gBAAC;gBAC5B,MAAM;oBACJ,QAAQ,6HAAA,CAAA,wBAAqB,CAAC,OAAO;gBACvC;YACF;YACA,gEAAgE;YAChE,uIAAuI;YACvI,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,YAAY,IAAI,gCAAgC;QAC9D;IACF;AACF,EAEA,qFAAqF;CACrF,qCAAqC;CACrC,+DAA+D;CAC/D,iDAAiD", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/speech/use-cases/GenerateVoice.ts"], "sourcesContent": ["import { PrismaClient, type VoiceGenerationQueue, VoiceGenerationStatus } from \"@prisma/client\";\r\nimport type { InAppQueueService } from \"~/infrastructure/queue/InAppQueueService\";\r\nimport { ee } from \"~/utils/pubsub\";\r\n\r\nexport class GenerateVoice {\r\n  constructor(private prisma: PrismaClient, private queueService: InAppQueueService) {}\r\n\r\n  async execute(sentenceId: string, userId: string): Promise<VoiceGenerationQueue> {\r\n    // Check if the user already has a pending or processing task\r\n    await this.prisma.voiceGenerationQueue.findFirst({\r\n      where: {\r\n        userId: userId,\r\n        status: {\r\n          in: [VoiceGenerationStatus.PENDING, VoiceGenerationStatus.PROCESSING],\r\n        },\r\n      },\r\n    });\r\n\r\n    // Create a new queue task\r\n    const queueTask = await this.queueService.addTask(sentenceId, userId);\r\n\r\n    ee.emit('queueTaskUpdate', queueTask);\r\n    void this.queueService.processQueue();\r\n    return queueTask;\r\n  }\r\n\r\n\r\n  async batchGenerate(projectId: string, userId: string): Promise<VoiceGenerationQueue[]> {\r\n    const project = await this.prisma.project.findUnique({\r\n      where: { id: projectId },\r\n      include: {\r\n        sentences: true,\r\n      },\r\n    });\r\n  \r\n    if (!project) {\r\n      throw new Error('Project not found');\r\n    }\r\n  \r\n    const sentences = project.sentences.map((sentence) => sentence.id);\r\n    const queueTasks: VoiceGenerationQueue[] = [];\r\n    for (const sentenceId of sentences) {\r\n      const task = await this.execute(sentenceId, userId);\r\n      queueTasks.push(task);\r\n    }\r\n    return queueTasks;\r\n  }\r\n  \r\n  \r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,MAAM;;;IACX,YAAY,AAAQ,MAAoB,EAAE,AAAQ,YAA+B,CAAE;aAA/D,SAAA;aAA8B,eAAA;IAAkC;IAEpF,MAAM,QAAQ,UAAkB,EAAE,MAAc,EAAiC;QAC/E,6DAA6D;QAC7D,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,QAAQ;gBACR,QAAQ;oBACN,IAAI;wBAAC,6HAAA,CAAA,wBAAqB,CAAC,OAAO;wBAAE,6HAAA,CAAA,wBAAqB,CAAC,UAAU;qBAAC;gBACvE;YACF;QACF;QAEA,0BAA0B;QAC1B,MAAM,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY;QAE9D,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,mBAAmB;QAC3B,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY;QACnC,OAAO;IACT;IAGA,MAAM,cAAc,SAAiB,EAAE,MAAc,EAAmC;QACtF,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,WAAa,SAAS,EAAE;QACjE,MAAM,aAAqC,EAAE;QAC7C,KAAK,MAAM,cAAc,UAAW;YAClC,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY;YAC5C,WAAW,IAAI,CAAC;QAClB;QACA,OAAO;IACT;AAGF", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/user/entities/User.ts"], "sourcesContent": ["import { BaseEntity } from '../../shared/BaseEntity';\r\nimport { Email } from '../value-objects/Email';\r\n\r\ninterface UserProps {\r\n  name: string;\r\n  email: Email;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n}\r\n\r\nexport class User extends BaseEntity {\r\n  private props: UserProps;\r\n\r\n  private constructor(props: UserProps, id: string, createdAt: Date, updatedAt: Date) {\r\n    super(id, createdAt, updatedAt);\r\n    this.props = props;\r\n  }\r\n\r\n  public static create(props: UserProps, id?: string, createdAt?: Date, updatedAt?: Date): User {\r\n    const newId = id ?? Math.random().toString(36).substring(2, 15); // Simple ID generation for now\r\n    const newCreatedAt = createdAt ?? new Date();\r\n    const newUpdatedAt = updatedAt ?? new Date();\r\n    return new User(props, newId, newCreatedAt, newUpdatedAt);\r\n  }\r\n\r\n  get name(): string {\r\n    return this.props.name;\r\n  }\r\n\r\n  get email(): Email {\r\n    return this.props.email;\r\n  }\r\n\r\n\r\n  public updateName(name: string): void {\r\n    this.props.name = name;\r\n    this.updatedAt = new Date(); // Update BaseEntity's updatedAt\r\n  }\r\n\r\n  public updateEmail(email: Email): void {\r\n    this.props.email = email;\r\n    this.updatedAt = new Date(); // Update BaseEntity's updatedAt\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,aAAa,uIAAA,CAAA,aAAU;IAC1B,MAAiB;IAEzB,YAAoB,KAAgB,EAAE,EAAU,EAAE,SAAe,EAAE,SAAe,CAAE;QAClF,KAAK,CAAC,IAAI,WAAW;QACrB,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,OAAc,OAAO,KAAgB,EAAE,EAAW,EAAE,SAAgB,EAAE,SAAgB,EAAQ;QAC5F,MAAM,QAAQ,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,+BAA+B;QAChG,MAAM,eAAe,aAAa,IAAI;QACtC,MAAM,eAAe,aAAa,IAAI;QACtC,OAAO,IAAI,KAAK,OAAO,OAAO,cAAc;IAC9C;IAEA,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IACxB;IAEA,IAAI,QAAe;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IACzB;IAGO,WAAW,IAAY,EAAQ;QACpC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,gCAAgC;IAC/D;IAEO,YAAY,KAAY,EAAQ;QACrC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,gCAAgC;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/shared/ValueObject.ts"], "sourcesContent": ["\r\ninterface ValueObjectProps {\r\n  [key: string]: unknown;\r\n}\r\n\r\nexport abstract class ValueObject<T extends ValueObjectProps> {\r\n  public readonly props: T;\r\n\r\n  constructor(props: T) {\r\n    this.props = Object.freeze(props);\r\n  }\r\n\r\n  public equals(vo?: ValueObject<T>): boolean {\r\n    if (vo === null || vo === undefined) {\r\n      return false;\r\n    }\r\n    if (this.props === vo.props) {\r\n      return true;\r\n    }\r\n    return JSON.stringify(this.props) === JSON.stringify(vo.props);\r\n  }\r\n}"], "names": [], "mappings": ";;;AAKO,MAAe;IACJ,MAAS;IAEzB,YAAY,KAAQ,CAAE;QACpB,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC;IAC7B;IAEO,OAAO,EAAmB,EAAW;QAC1C,IAAI,OAAO,QAAQ,OAAO,WAAW;YACnC,OAAO;QACT;QACA,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,EAAE;YAC3B,OAAO;QACT;QACA,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,KAAK;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 1790, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/domain/user/value-objects/Email.ts"], "sourcesContent": ["import { ValueObject } from '../../shared/ValueObject'; // Assuming you'll create a ValueObject base class\r\n\r\ntype EmailProps = {\r\n  value: string;\r\n}\r\n\r\nexport class Email extends ValueObject<EmailProps> {\r\n  private constructor(props: EmailProps) {\r\n    super(props);\r\n  }\r\n\r\n  public static create(email: string): Email {\r\n    if (!this.isValidEmail(email)) {\r\n      throw new Error('Invalid email address');\r\n    }\r\n    return new Email({ value: email });\r\n  }\r\n\r\n  private static isValidEmail(email: string): boolean {\r\n    // Basic email validation regex. Use a more robust one for production.\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  }\r\n\r\n  get value(): string {\r\n    return this.props.value;\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA,+OAAwD,kDAAkD;;AAMnG,MAAM,cAAc,wIAAA,CAAA,cAAW;IACpC,YAAoB,KAAiB,CAAE;QACrC,KAAK,CAAC;IACR;IAEA,OAAc,OAAO,KAAa,EAAS;QACzC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC7B,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,MAAM;YAAE,OAAO;QAAM;IAClC;IAEA,OAAe,aAAa,KAAa,EAAW;QAClD,sEAAsE;QACtE,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,IAAI,QAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/persistence/repositories/PrismaUserRepository.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\r\nimport type { IUserRepository } from '../../../domain/user/repositories/IUserRepository';\r\nimport { User } from '../../../domain/user/entities/User';\r\nimport { Email } from '../../../domain/user/value-objects/Email';\r\n\r\nexport class PrismaUserRepository implements IUserRepository {\r\n  constructor(private prisma: PrismaClient) {}\r\n\r\n  async findById(id: string): Promise<User | null> {\r\n    const userData = await this.prisma.user.findUnique({\r\n      where: { id },\r\n    });\r\n    if (!userData) {\r\n      return null;\r\n    }\r\n    return User.create(\r\n      {\r\n        name: userData.name,\r\n        email: Email.create(userData.email),\r\n        createdAt: userData.createdAt,\r\n        updatedAt: userData.updatedAt,\r\n      },\r\n      userData.id,\r\n    );\r\n  }\r\n\r\n  async findByEmail(email: Email): Promise<User | null> {\r\n    const userData = await this.prisma.user.findUnique({\r\n      where: { email: email.value },\r\n    });\r\n    if (!userData) {\r\n      return null;\r\n    }\r\n    return User.create(\r\n      {\r\n        name: userData.name,\r\n        email: Email.create(userData.email),\r\n        createdAt: userData.createdAt,\r\n        updatedAt: userData.updatedAt,\r\n      },\r\n      userData.id,\r\n    );\r\n  }\r\n\r\n  async save(user: User): Promise<void> {\r\n    await this.prisma.user.upsert({\r\n      where: { id: user.id },\r\n      update: {\r\n        name: user.name,\r\n        email: user.email.value,\r\n        updatedAt: user.updatedAt,\r\n      },\r\n      create: {\r\n        id: user.id,\r\n        name: user.name,\r\n        email: user.email.value,\r\n        createdAt: user.createdAt,\r\n        updatedAt: user.updatedAt,\r\n      },\r\n    });\r\n  }\r\n\r\n  async delete(id: string): Promise<void> {\r\n    await this.prisma.user.delete({\r\n      where: { id },\r\n    });\r\n  }\r\n}"], "names": [], "mappings": ";;;AAEA;AACA;;;AAEO,MAAM;;IACX,YAAY,AAAQ,MAAoB,CAAE;aAAtB,SAAA;IAAuB;IAE3C,MAAM,SAAS,EAAU,EAAwB;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACjD,OAAO;gBAAE;YAAG;QACd;QACA,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QACA,OAAO,2IAAA,CAAA,OAAI,CAAC,MAAM,CAChB;YACE,MAAM,SAAS,IAAI;YACnB,OAAO,oJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,SAAS,KAAK;YAClC,WAAW,SAAS,SAAS;YAC7B,WAAW,SAAS,SAAS;QAC/B,GACA,SAAS,EAAE;IAEf;IAEA,MAAM,YAAY,KAAY,EAAwB;QACpD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACjD,OAAO;gBAAE,OAAO,MAAM,KAAK;YAAC;QAC9B;QACA,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QACA,OAAO,2IAAA,CAAA,OAAI,CAAC,MAAM,CAChB;YACE,MAAM,SAAS,IAAI;YACnB,OAAO,oJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,SAAS,KAAK;YAClC,WAAW,SAAS,SAAS;YAC7B,WAAW,SAAS,SAAS;QAC/B,GACA,SAAS,EAAE;IAEf;IAEA,MAAM,KAAK,IAAU,EAAiB;QACpC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,QAAQ;gBACN,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK,CAAC,KAAK;gBACvB,WAAW,KAAK,SAAS;YAC3B;YACA,QAAQ;gBACN,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK,CAAC,KAAK;gBACvB,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;YAC3B;QACF;IACF;IAEA,MAAM,OAAO,EAAU,EAAiB;QACtC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE;YAAG;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/user/use-cases/CreateUser.ts"], "sourcesContent": ["import type { IUserRepository } from '../../../domain/user/repositories/IUserRepository';\r\nimport { User } from '../../../domain/user/entities/User';\r\nimport { Email } from '../../../domain/user/value-objects/Email';\r\nimport type { CreateUserDto } from '../dtos/CreateUserDto';\r\nimport type { UserResponseDto } from '../dtos/UserResponseDto';\r\n\r\nexport class CreateUser {\r\n  constructor(private userRepository: IUserRepository) {}\r\n\r\n  public async execute(input: CreateUserDto): Promise<UserResponseDto> {\r\n    const email = Email.create(input.email); // Value object creation\r\n    const existingUser = await this.userRepository.findByEmail(email);\r\n\r\n    if (existingUser) {\r\n      throw new Error('User with this email already exists.');\r\n    }\r\n\r\n    const user = User.create({\r\n      name: input.name,\r\n      email: email,\r\n    });\r\n\r\n    await this.userRepository.save(user);\r\n\r\n    return {\r\n      id: user.id,\r\n      name: user.name,\r\n      email: user.email.value,\r\n      createdAt: user.createdAt,\r\n      updatedAt: user.updatedAt,\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;AACA;AACA;;;AAIO,MAAM;;IACX,YAAY,AAAQ,cAA+B,CAAE;aAAjC,iBAAA;IAAkC;IAEtD,MAAa,QAAQ,KAAoB,EAA4B;QACnE,MAAM,QAAQ,oJAAA,CAAA,QAAK,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG,wBAAwB;QACjE,MAAM,eAAe,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAE3D,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,2IAAA,CAAA,OAAI,CAAC,MAAM,CAAC;YACvB,MAAM,MAAM,IAAI;YAChB,OAAO;QACT;QAEA,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QAE/B,OAAO;YACL,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK,CAAC,KAAK;YACvB,WAAW,KAAK,SAAS;YACzB,WAAW,KAAK,SAAS;QAC3B;IACF;AACF", "debugId": null}}, {"offset": {"line": 1937, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/application/sentence/use-cases/UpdateSentence.ts"], "sourcesContent": ["\r\nimport type { ISentenceRepository } from \"~/domain/sentence/ISentenceRepository\";\r\nimport type { Sentence } from \"~/domain/sentence/entities/Sentence\";\r\n\r\ninterface UpdateSentenceInput {\r\n  sentenceId: string;\r\n  voiceName?: string;\r\n  seed?: number;\r\n  model?: string;\r\n}\r\n\r\nexport class UpdateSentence {\r\n  constructor(private sentenceRepository: ISentenceRepository) {}\r\n\r\n  async execute(input: UpdateSentenceInput): Promise<Sentence> {\r\n    const existingSentence = await this.sentenceRepository.findById(input.sentenceId);\r\n\r\n    if (!existingSentence) {\r\n      throw new Error('Sentence not found');\r\n    }\r\n\r\n    // Update the generateConfig properties\r\n    existingSentence.generateConfig = {\r\n      voiceName: input.voiceName ?? existingSentence.generateConfig?.voiceName,\r\n      seed: input.seed ?? existingSentence.generateConfig?.seed,\r\n      model: input.model ?? existingSentence.generateConfig?.model,\r\n    };\r\n\r\n    const updatedSentence = await this.sentenceRepository.update(existingSentence);\r\n\r\n    return updatedSentence;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAWO,MAAM;;IACX,YAAY,AAAQ,kBAAuC,CAAE;aAAzC,qBAAA;IAA0C;IAE9D,MAAM,QAAQ,KAA0B,EAAqB;QAC3D,MAAM,mBAAmB,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,UAAU;QAEhF,IAAI,CAAC,kBAAkB;YACrB,MAAM,IAAI,MAAM;QAClB;QAEA,uCAAuC;QACvC,iBAAiB,cAAc,GAAG;YAChC,WAAW,MAAM,SAAS,IAAI,iBAAiB,cAAc,EAAE;YAC/D,MAAM,MAAM,IAAI,IAAI,iBAAiB,cAAc,EAAE;YACrD,OAAO,MAAM,KAAK,IAAI,iBAAiB,cAAc,EAAE;QACzD;QAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAE7D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1966, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/trpc.ts"], "sourcesContent": ["/**\r\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\r\n * 1. You want to modify request context (see Part 1).\r\n * 2. You want to create a new middleware or type of procedure (see Part 3).\r\n *\r\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\r\n * need to use are documented accordingly near the end.\r\n */\r\n\r\nimport { initTRPC, TRPCError } from \"@trpc/server\";\r\nimport superjson from \"superjson\";\r\nimport { ZodError } from \"zod\";\r\n\r\nimport { auth } from \"../auth/index\";\r\nimport { db } from \"../persistence/prisma/client\";\r\nimport type { PrismaClient } from \"@prisma/client\";\r\nimport { PrismaProjectRepository } from \"../persistence/repositories/PrismaProjectRepository\";\r\nimport { PrismaSentenceRepository } from \"../persistence/repositories/PrismaSentenceRepository\";\r\nimport { CreateProject } from \"../../application/project/use-cases/CreateProject\";\r\nimport { DeleteProject } from \"../../application/project/use-cases/DeleteProject\";\r\nimport { UpdateProject } from \"../../application/project/use-cases/UpdateProject\";\r\nimport { CreateSentence } from \"../../application/sentence/use-cases/CreateSentence\";\r\nimport { PrismaSpeechRepository } from \"../persistence/repositories/PrismaSpeechRepository\";\r\nimport { CreateSpeech } from \"../../application/speech/use-cases/CreateSpeech\";\r\nimport { InAppQueueService } from \"../queue/InAppQueueService\";\r\nimport { VoiceGenerationQueueProcessor } from \"../queue/VoiceGenerationQueueProcessor\";\r\nimport { GenerateVoice } from \"../../application/speech/use-cases/GenerateVoice\";\r\nimport { PrismaUserRepository } from \"../persistence/repositories/PrismaUserRepository\";\r\nimport { CreateUser } from \"../../application/user/use-cases/CreateUser\";\r\nimport { UpdateSentence } from \"~/application/sentence/use-cases/UpdateSentence\";\r\n\r\n/**\r\n * 1. CONTEXT\r\n *\r\n * This section defines the \"contexts\" that are available in the backend API.\r\n *\r\n * These allow you to access things when processing a request, like the database, the session, etc.\r\n *\r\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\r\n * wrap this and provides the required context.\r\n *\r\n * @see https://trpc.io/docs/server/context\r\n */\r\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\r\n  const session = await auth();\r\n\r\n  // Instantiate repositories\r\n  const sentenceRepository = new PrismaSentenceRepository(db);\r\n  const projectRepository = new PrismaProjectRepository(db, sentenceRepository);\r\n\r\n  // Instantiate use cases, injecting their dependencies\r\n  const createProjectUseCase = new CreateProject(projectRepository, sentenceRepository);\r\n  const deleteProjectUseCase = new DeleteProject(projectRepository);\r\n  const updateProjectUseCase = new UpdateProject(projectRepository);\r\n  const createSentenceUseCase = new CreateSentence(sentenceRepository);\r\n\r\n  // Speech related dependencies\r\n  const speechRepository = new PrismaSpeechRepository(db);\r\n  const createSpeechUseCase = new CreateSpeech(speechRepository);\r\n  const updateSentenceUseCase = new UpdateSentence(sentenceRepository);\r\n\r\n  const voiceGenerationQueueProcessor = new VoiceGenerationQueueProcessor(db);\r\n  const inAppQueueService = new InAppQueueService(db, voiceGenerationQueueProcessor);\r\n  const generateVoiceUseCase = new GenerateVoice(db, inAppQueueService);\r\n\r\n  // User related dependencies\r\n  const userRepository = new PrismaUserRepository(db);\r\n  const createUserUseCase = new CreateUser(userRepository);\r\n\r\n  return {\r\n    db: db as PrismaClient, // Explicitly cast db to PrismaClient\r\n    session,\r\n    projectRepository,\r\n    sentenceRepository,\r\n    createProjectUseCase,\r\n    deleteProjectUseCase,\r\n    updateProjectUseCase,\r\n    createSentenceUseCase,\r\n    updateSentenceUseCase,\r\n    speechRepository,\r\n    createSpeechUseCase,\r\n    inAppQueueService,\r\n    generateVoiceUseCase,\r\n    // User related dependencies\r\n    userRepository,\r\n    createUserUseCase,\r\n    ...opts,\r\n  };\r\n};\r\n\r\n/**\r\n * 2. INITIALIZATION\r\n *\r\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\r\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\r\n * errors on the backend.\r\n */\r\nconst t = initTRPC.context<typeof createTRPCContext>().create({\r\n  transformer: superjson,\r\n  errorFormatter({ shape, error }) {\r\n    return {\r\n      ...shape,\r\n      data: {\r\n        ...shape.data,\r\n        zodError:\r\n          error.cause instanceof ZodError ? error.cause.flatten() : null,\r\n      },\r\n    };\r\n  },\r\n});\r\n\r\n/**\r\n * Create a server-side caller.\r\n *\r\n * @see https://trpc.io/docs/server/server-side-calls\r\n */\r\nexport const createCallerFactory = t.createCallerFactory;\r\n\r\n/**\r\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\r\n *\r\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\r\n * \"/src/server/api/routers\" directory.\r\n */\r\n\r\n/**\r\n * This is how you create new routers and sub-routers in your tRPC API.\r\n *\r\n * @see https://trpc.io/docs/router\r\n */\r\nexport const createTRPCRouter = t.router;\r\n\r\n/**\r\n * Middleware for timing procedure execution and adding an artificial delay in development.\r\n *\r\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\r\n * network latency that would occur in production but not in local development.\r\n */\r\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\r\n  const start = Date.now();\r\n\r\n  if (t._config.isDev) {\r\n    // artificial delay in dev\r\n    const waitMs = Math.floor(Math.random() * 400) + 100;\r\n    await new Promise((resolve) => setTimeout(resolve, waitMs));\r\n  }\r\n\r\n  const result = await next();\r\n\r\n  const end = Date.now();\r\n  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\r\n\r\n  return result;\r\n});\r\n\r\n/**\r\n * Public (unauthenticated) procedure\r\n *\r\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\r\n * guarantee that a user querying is authorized, but you can still access user session data if they\r\n * are logged in.\r\n */\r\nexport const publicProcedure = t.procedure.use(timingMiddleware);\r\n\r\n/**\r\n * Protected (authenticated) procedure\r\n *\r\n * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies\r\n * the session is valid and guarantees `ctx.session.user` is not null.\r\n *\r\n * @see https://trpc.io/docs/procedures\r\n */\r\nexport const protectedProcedure = t.procedure\r\n  .use(timingMiddleware)\r\n  .use(({ ctx, next }) => {\r\n    if (!ctx.session?.user) {\r\n      throw new TRPCError({ code: \"UNAUTHORIZED\" });\r\n    }\r\n    return next({\r\n      ctx: {\r\n        // infers the `session` as non-nullable\r\n        session: { ...ctx.session, user: ctx.session.user },\r\n      },\r\n    });\r\n  });\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAcO,MAAM,oBAAoB,OAAO;IACtC,MAAM,UAAU,MAAM,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD;IAEzB,2BAA2B;IAC3B,MAAM,qBAAqB,IAAI,kLAAA,CAAA,2BAAwB,CAAC,0JAAA,CAAA,KAAE;IAC1D,MAAM,oBAAoB,IAAI,iLAAA,CAAA,0BAAuB,CAAC,0JAAA,CAAA,KAAE,EAAE;IAE1D,sDAAsD;IACtD,MAAM,uBAAuB,IAAI,gKAAA,CAAA,gBAAa,CAAC,mBAAmB;IAClE,MAAM,uBAAuB,IAAI,gKAAA,CAAA,gBAAa,CAAC;IAC/C,MAAM,uBAAuB,IAAI,gKAAA,CAAA,gBAAa,CAAC;IAC/C,MAAM,wBAAwB,IAAI,kKAAA,CAAA,iBAAc,CAAC;IAEjD,8BAA8B;IAC9B,MAAM,mBAAmB,IAAI,gLAAA,CAAA,yBAAsB,CAAC,0JAAA,CAAA,KAAE;IACtD,MAAM,sBAAsB,IAAI,8JAAA,CAAA,eAAY,CAAC;IAC7C,MAAM,wBAAwB,IAAI,kKAAA,CAAA,iBAAc,CAAC;IAEjD,MAAM,gCAAgC,IAAI,iKAAA,CAAA,gCAA6B,CAAC,0JAAA,CAAA,KAAE;IAC1E,MAAM,oBAAoB,IAAI,qJAAA,CAAA,oBAAiB,CAAC,0JAAA,CAAA,KAAE,EAAE;IACpD,MAAM,uBAAuB,IAAI,+JAAA,CAAA,gBAAa,CAAC,0JAAA,CAAA,KAAE,EAAE;IAEnD,4BAA4B;IAC5B,MAAM,iBAAiB,IAAI,8KAAA,CAAA,uBAAoB,CAAC,0JAAA,CAAA,KAAE;IAClD,MAAM,oBAAoB,IAAI,0JAAA,CAAA,aAAU,CAAC;IAEzC,OAAO;QACL,IAAI,0JAAA,CAAA,KAAE;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,4BAA4B;QAC5B;QACA;QACA,GAAG,IAAI;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,0RAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC5D,aAAa,kMAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,UACE,MAAM,KAAK,YAAY,wMAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YAC9D;QACF;IACF;AACF;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACzD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACnB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACrD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACT;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC;AAUxC,MAAM,qBAAqB,EAAE,SAAS,CAC1C,GAAG,CAAC,kBACJ,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,OAAO,EAAE,MAAM;QACtB,MAAM,IAAI,oSAAA,CAAA,YAAS,CAAC;YAAE,MAAM;QAAe;IAC7C;IACA,OAAO,KAAK;QACV,KAAK;YACH,uCAAuC;YACvC,SAAS;gBAAE,GAAG,IAAI,OAAO;gBAAE,MAAM,IAAI,OAAO,CAAC,IAAI;YAAC;QACpD;IACF;AACF", "debugId": null}}, {"offset": {"line": 2121, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/routers/user.ts"], "sourcesContent": ["import { z } from 'zod';\r\nimport { createTRPCRouter, publicProcedure, protectedProcedure } from '../trpc';\r\nimport { TokenType, type UserTokenAvailable } from '@prisma/client';\r\nimport { on } from 'events';\r\nimport { ee } from '~/utils/pubsub';\r\n\r\nexport const userRouter = createTRPCRouter({\r\n  create: publicProcedure\r\n    .input(z.object({ name: z.string(), email: z.string().email() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const user = await ctx.createUserUseCase.execute(input);\r\n      return user;\r\n    }),\r\n  getUserTokens: protectedProcedure\r\n    .query(async ({ ctx }) => {\r\n      const userId = ctx.session.user.id;\r\n      const userTokens = await ctx.db.userTokenAvailable.findMany({\r\n        where: { userId },\r\n      });\r\n      return userTokens;\r\n    }),\r\n  createTrialToken: protectedProcedure\r\n    .mutation(async ({ ctx }) => {\r\n      const userId = ctx.session.user.id;\r\n      const amount = 100;\r\n      const tokenType = \"GOOGLE_GENERATIVE_AI\";\r\n\r\n      // Find or create UserTokenAvailable\r\n      let userToken = await ctx.db.userTokenAvailable.findUnique({\r\n        where: {\r\n          userId_tokenType: {\r\n            userId,\r\n            tokenType,\r\n          },\r\n        },\r\n      });\r\n\r\n      if (userToken) {\r\n        userToken = await ctx.db.userTokenAvailable.update({\r\n          where: {\r\n            id: userToken.id,\r\n          },\r\n          data: {\r\n            amount: userToken.amount + amount,\r\n          },\r\n        });\r\n      } else {\r\n        userToken = await ctx.db.userTokenAvailable.create({\r\n          data: {\r\n            userId,\r\n            tokenType,\r\n            amount,\r\n          },\r\n        });\r\n      }\r\n\r\n      // Create UserTokenLedger entry\r\n      await ctx.db.userTokenLedger.create({\r\n        data: {\r\n          userId,\r\n          tokenType,\r\n          amount,\r\n          message: \"Trial token granted\",\r\n        },\r\n      });\r\n\r\n      return userToken;\r\n    }),\r\n topUpTokens: protectedProcedure\r\n   .input(z.object({ amount: z.number(), tokenType: z.nativeEnum(TokenType) }))\r\n   .mutation(async ({ input, ctx }) => {\r\n     const userId = ctx.session.user.id;\r\n     const { amount, tokenType } = input;\r\n\r\n     // Find or create UserTokenAvailable\r\n     let userToken = await ctx.db.userTokenAvailable.findUnique({\r\n       where: {\r\n         userId_tokenType: {\r\n           userId,\r\n           tokenType,\r\n         },\r\n       },\r\n     });\r\n\r\n     if (userToken) {\r\n       userToken = await ctx.db.userTokenAvailable.update({\r\n         where: {\r\n           id: userToken.id,\r\n         },\r\n         data: {\r\n           amount: userToken.amount + amount,\r\n         },\r\n       });\r\n     } else {\r\n       userToken = await ctx.db.userTokenAvailable.create({\r\n         data: {\r\n           userId,\r\n           tokenType,\r\n           amount,\r\n         },\r\n       });\r\n     }\r\n\r\n     // Create UserTokenLedger entry\r\n     await ctx.db.userTokenLedger.create({\r\n       data: {\r\n         userId,\r\n         tokenType,\r\n         amount,\r\n         message: \"Tokens topped up\",\r\n       },\r\n     });\r\n\r\n     return userToken;\r\n   }),\r\n   userAvailableTokenUpdate: protectedProcedure\r\n   .input(z.object({ userId: z.string() }))\r\n   .subscription(async function* ({input, signal, ctx }) {\r\n      for await (const [data] of on(ee, 'userTokenAvailableUpdate', {\r\n          // Passing the AbortSignal from the request automatically cancels the event emitter when the request is aborted\r\n          signal: signal,\r\n      })) {\r\n        const userToken = data as UserTokenAvailable;\r\n        if (userToken.userId !== input.userId) continue;\r\n        yield userToken;\r\n      }\r\n   }),\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,aAAa,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,QAAQ,uIAAA,CAAA,kBAAe,CACpB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM;QAAI,OAAO,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;IAAG,IAC7D,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,OAAO,MAAM,IAAI,iBAAiB,CAAC,OAAO,CAAC;QACjD,OAAO;IACT;IACF,eAAe,uIAAA,CAAA,qBAAkB,CAC9B,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACnB,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC1D,OAAO;gBAAE;YAAO;QAClB;QACA,OAAO;IACT;IACF,kBAAkB,uIAAA,CAAA,qBAAkB,CACjC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACtB,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,SAAS;QACf,MAAM,YAAY;QAElB,oCAAoC;QACpC,IAAI,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;YACzD,OAAO;gBACL,kBAAkB;oBAChB;oBACA;gBACF;YACF;QACF;QAEA,IAAI,WAAW;YACb,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACjD,OAAO;oBACL,IAAI,UAAU,EAAE;gBAClB;gBACA,MAAM;oBACJ,QAAQ,UAAU,MAAM,GAAG;gBAC7B;YACF;QACF,OAAO;YACL,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACjD,MAAM;oBACJ;oBACA;oBACA;gBACF;YACF;QACF;QAEA,+BAA+B;QAC/B,MAAM,IAAI,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ;gBACA;gBACA;gBACA,SAAS;YACX;QACF;QAEA,OAAO;IACT;IACH,aAAa,uIAAA,CAAA,qBAAkB,CAC5B,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,qOAAA,CAAA,IAAC,CAAC,MAAM;QAAI,WAAW,qOAAA,CAAA,IAAC,CAAC,UAAU,CAAC,6HAAA,CAAA,YAAS;IAAE,IACxE,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;QAE9B,oCAAoC;QACpC,IAAI,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;YACzD,OAAO;gBACL,kBAAkB;oBAChB;oBACA;gBACF;YACF;QACF;QAEA,IAAI,WAAW;YACb,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACjD,OAAO;oBACL,IAAI,UAAU,EAAE;gBAClB;gBACA,MAAM;oBACJ,QAAQ,UAAU,MAAM,GAAG;gBAC7B;YACF;QACF,OAAO;YACL,YAAY,MAAM,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACjD,MAAM;oBACJ;oBACA;oBACA;gBACF;YACF;QACF;QAEA,+BAA+B;QAC/B,MAAM,IAAI,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ;gBACA;gBACA;gBACA,SAAS;YACX;QACF;QAEA,OAAO;IACT;IACA,0BAA0B,uIAAA,CAAA,qBAAkB,CAC3C,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACpC,YAAY,CAAC,gBAAiB,EAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE;QACjD,WAAW,MAAM,CAAC,KAAK,IAAI,CAAA,GAAA,qGAAA,CAAA,KAAE,AAAD,EAAE,wHAAA,CAAA,KAAE,EAAE,4BAA4B;YAC1D,+GAA+G;YAC/G,QAAQ;QACZ,GAAI;YACF,MAAM,YAAY;YAClB,IAAI,UAAU,MAAM,KAAK,MAAM,MAAM,EAAE;YACvC,MAAM;QACR;IACH;AACH", "debugId": null}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/routers/sentence.ts"], "sourcesContent": ["import { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../trpc';\r\nimport { ee } from '~/utils/pubsub'; // Import the event emitter\r\n\r\nexport const sentenceRouter = createTRPCRouter({\r\n  createSentence: protectedProcedure\r\n    .input(z.object({ projectId: z.string(), text: z.string() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const newSentence = await ctx.createSentenceUseCase.execute(input);\r\n      return newSentence;\r\n    }),\r\n\r\n  deleteSentence: protectedProcedure\r\n    .input(z.object({ sentenceId: z.string() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      // Fetch the sentence before deleting to get the projectId\r\n      const sentenceToDelete = await ctx.sentenceRepository.findById(input.sentenceId);\r\n      if (sentenceToDelete) {\r\n        await ctx.sentenceRepository.delete(input.sentenceId);\r\n        ee.emit('sentenceUpdate', { projectId: sentenceToDelete.projectId, sentence: { id: input.sentenceId }, type: 'delete' }); // Emit event\r\n      }\r\n      return { id: input.sentenceId }; // Return deleted ID or success indicator\r\n    }),\r\n\r\n  updateSentence: protectedProcedure\r\n    .input(z.object({\r\n      sentenceId: z.string(),\r\n      voiceName: z.string().optional(),\r\n      seed: z.number().optional(),\r\n      model: z.string().optional(),\r\n    }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const updatedSentence = await ctx.updateSentenceUseCase.execute(input);\r\n      ee.emit('sentenceUpdate', { projectId: updatedSentence.projectId, sentence: updatedSentence, type: 'update' });\r\n      return updatedSentence;\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA,kNAAqC,2BAA2B;;;;AAEzD,MAAM,iBAAiB,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;IAC7C,gBAAgB,uIAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;QAAI,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACzD,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,cAAc,MAAM,IAAI,qBAAqB,CAAC,OAAO,CAAC;QAC5D,OAAO;IACT;IAEF,gBAAgB,uIAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,YAAY,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACxC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,0DAA0D;QAC1D,MAAM,mBAAmB,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,MAAM,UAAU;QAC/E,IAAI,kBAAkB;YACpB,MAAM,IAAI,kBAAkB,CAAC,MAAM,CAAC,MAAM,UAAU;YACpD,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,kBAAkB;gBAAE,WAAW,iBAAiB,SAAS;gBAAE,UAAU;oBAAE,IAAI,MAAM,UAAU;gBAAC;gBAAG,MAAM;YAAS,IAAI,aAAa;QACzI;QACA,OAAO;YAAE,IAAI,MAAM,UAAU;QAAC,GAAG,yCAAyC;IAC5E;IAEF,gBAAgB,uIAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,YAAY,qOAAA,CAAA,IAAC,CAAC,MAAM;QACpB,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,IACC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,kBAAkB,MAAM,IAAI,qBAAqB,CAAC,OAAO,CAAC;QAChE,wHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,kBAAkB;YAAE,WAAW,gBAAgB,SAAS;YAAE,UAAU;YAAiB,MAAM;QAAS;QAC5G,OAAO;IACT;AACJ", "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/routers/speech.ts"], "sourcesContent": ["import { z } from 'zod';\r\nimport { createTRPCRouter, protectedProcedure } from '../trpc';\r\nimport { sentenceRouter } from './sentence'; // Import the new sentence router\r\nimport { observable } from '@trpc/server/observable';\r\nimport { type VoiceGenerationQueue } from '@prisma/client'; // Import the new model\r\nimport EventEmitter, { on } from 'events';\r\nimport { ee } from '~/utils/pubsub';\r\n\r\n\r\nexport const speechRouter = createTRPCRouter({\r\n  create: protectedProcedure // Changed to protectedProcedure\r\n    .input(z.object({ userId: z.string(), text: z.string(), audioUrl: z.string().url() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const speech = await ctx.createSpeechUseCase.execute(input);\r\n      return speech;\r\n    }),\r\n\r\n  generateVoice: protectedProcedure\r\n    .input(z.object({ sentenceId: z.string() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const queueTask = await ctx.generateVoiceUseCase.execute(input.sentenceId, ctx.session.user.id);\r\n      // TODO: Publish initial task status to the subscription\r\n      // ee.emit('queueTaskUpdate', queueTask);\r\n      return queueTask;\r\n    }),\r\n  generateBatchSentence: protectedProcedure\r\n    .input(z.object({ projectId: z.string() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const queueTasks = await ctx.generateVoiceUseCase.batchGenerate(input.projectId, ctx.session.user.id);\r\n      return queueTasks;\r\n    }),\r\n  // Add a subscription for voice generation queue task updates\r\n  onQueueTaskUpdate: protectedProcedure\r\n    .input(z.object({ projectId: z.string() }))\r\n    .subscription(async function* ({ input, signal, ctx }){\r\n      console.log('event emitter', ee);\r\n      for await (const [data] of on(ee, 'queueTaskUpdate', {\r\n          // Passing the AbortSignal from the request automatically cancels the event emitter when the request is aborted\r\n          signal: signal,\r\n      })) {\r\n        const post = data as VoiceGenerationQueue;\r\n        console.log({post});\r\n        const sentence = await ctx.sentenceRepository.findById(post.sentenceId)\r\n        if (sentence?.projectId === input.projectId) {\r\n          yield sentence;\r\n        }\r\n      }\r\n    }),\r\n\r\n  sentence: sentenceRouter, // Merge the sentence router\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA,wQAA6C,iCAAiC;AAG9E;AACA;;;;;;AAGO,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;IAC3C,QAAQ,wIAAmB,gCAAgC;IAAnD,CAAA,qBAAkB,CACvB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,QAAQ,qOAAA,CAAA,IAAC,CAAC,MAAM;QAAI,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM;QAAI,UAAU,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;IAAG,IAClF,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,SAAS,MAAM,IAAI,mBAAmB,CAAC,OAAO,CAAC;QACrD,OAAO;IACT;IAEF,eAAe,uIAAA,CAAA,qBAAkB,CAC9B,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,YAAY,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACxC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,YAAY,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,MAAM,UAAU,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAC9F,wDAAwD;QACxD,yCAAyC;QACzC,OAAO;IACT;IACF,uBAAuB,uIAAA,CAAA,qBAAkB,CACtC,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,aAAa,MAAM,IAAI,oBAAoB,CAAC,aAAa,CAAC,MAAM,SAAS,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACpG,OAAO;IACT;IACF,6DAA6D;IAC7D,mBAAmB,uIAAA,CAAA,qBAAkB,CAClC,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,YAAY,CAAC,gBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE;QACnD,QAAQ,GAAG,CAAC,iBAAiB,wHAAA,CAAA,KAAE;QAC/B,WAAW,MAAM,CAAC,KAAK,IAAI,CAAA,GAAA,qGAAA,CAAA,KAAE,AAAD,EAAE,wHAAA,CAAA,KAAE,EAAE,mBAAmB;YACjD,+GAA+G;YAC/G,QAAQ;QACZ,GAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;gBAAC;YAAI;YACjB,MAAM,WAAW,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,KAAK,UAAU;YACtE,IAAI,UAAU,cAAc,MAAM,SAAS,EAAE;gBAC3C,MAAM;YACR;QACF;IACF;IAEF,UAAU,sJAAA,CAAA,iBAAc;AAC1B", "debugId": null}}, {"offset": {"line": 2380, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/routers/project.ts"], "sourcesContent": ["import { z } from \"zod\";\r\nimport { createTRPCRouter, protectedProcedure } from \"~/infrastructure/trpc/trpc\";\r\nimport { Prisma } from \"@prisma/client\";\r\n\r\nconst projectWithSentences = Prisma.validator<Prisma.ProjectDefaultArgs>()({\r\n  include: {\r\n    sentences: {\r\n      include: {\r\n        selectedVoiceGeneration: true,\r\n      },\r\n    },\r\n  },\r\n});\r\n\r\nexport type ProjectWithSentences = Prisma.ProjectGetPayload<typeof projectWithSentences>;\r\n\r\nexport const projectRouter = createTRPCRouter({\r\n  getById: protectedProcedure\r\n    .input(z.object({ projectId: z.string() }))\r\n    .query(async ({ ctx, input }) => {\r\n      const project = await ctx.projectRepository.getById(input.projectId);\r\n      return project?.toResponseObject();\r\n    }),\r\n\r\n  create: protectedProcedure\r\n    .input(z.object({\r\n      name: z.string().min(1, \"Project name cannot be empty\"),\r\n      description: z.string().optional(),\r\n      script: z.string().optional(),\r\n    }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const project = await ctx.createProjectUseCase.execute({\r\n        name: input.name,\r\n        description: input.description,\r\n        script: input.script,\r\n        userId: ctx.session.user.id,\r\n      });\r\n      return project;\r\n    }),\r\n\r\n  getAll: protectedProcedure\r\n    .query(async ({ ctx }) => {\r\n      return ctx.db.project.findMany({\r\n        where: { userId: ctx.session.user.id },\r\n        orderBy: { createdAt: \"desc\" },\r\n      });\r\n    }),\r\n\r\n  delete: protectedProcedure\r\n    .input(z.object({ projectId: z.string() }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      await ctx.deleteProjectUseCase.execute(input.projectId);\r\n      return { success: true };\r\n    }),\r\n\r\n  update: protectedProcedure\r\n    .input(z.object({\r\n      projectId: z.string(),\r\n      name: z.string().min(1, \"Project name cannot be empty\").optional(),\r\n      description: z.string().optional(),\r\n    }))\r\n    .mutation(async ({ input, ctx }) => {\r\n      const project = await ctx.updateProjectUseCase.execute({\r\n        projectId: input.projectId,\r\n        name: input.name,\r\n        description: input.description,\r\n      });\r\n      return project;\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEA,MAAM,uBAAuB,6HAAA,CAAA,SAAM,CAAC,SAAS,GAA8B;IACzE,SAAS;QACP,WAAW;YACT,SAAS;gBACP,yBAAyB;YAC3B;QACF;IACF;AACF;AAIO,MAAM,gBAAgB,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5C,SAAS,uIAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,MAAM,UAAU,MAAM,IAAI,iBAAiB,CAAC,OAAO,CAAC,MAAM,SAAS;QACnE,OAAO,SAAS;IAClB;IAEF,QAAQ,uIAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,aAAa,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAChC,QAAQ,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,IACC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,UAAU,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC;YACrD,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;YAC9B,QAAQ,MAAM,MAAM;YACpB,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACT;IAEF,QAAQ,uIAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7B,OAAO;gBAAE,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;YAAC;YACrC,SAAS;gBAAE,WAAW;YAAO;QAC/B;IACF;IAEF,QAAQ,uIAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;IAAG,IACvC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,MAAM,SAAS;QACtD,OAAO;YAAE,SAAS;QAAK;IACzB;IAEF,QAAQ,uIAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,qOAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACd,WAAW,qOAAA,CAAA,IAAC,CAAC,MAAM;QACnB,MAAM,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,gCAAgC,QAAQ;QAChE,aAAa,qOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,IACC,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;QAC7B,MAAM,UAAU,MAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC;YACrD,WAAW,MAAM,SAAS;YAC1B,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;QAChC;QACA,OAAO;IACT;AACJ", "debugId": null}}, {"offset": {"line": 2456, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/infrastructure/trpc/root.ts"], "sourcesContent": ["import { createCallerFactory, createTR<PERSON>Router } from \"./trpc\";\r\nimport { userRouter } from \"~/infrastructure/trpc/routers/user\";\r\nimport { speechRouter } from \"~/infrastructure/trpc/routers/speech\";\r\nimport { projectRouter } from \"~/infrastructure/trpc/routers/project\";\r\nimport { sentenceRouter } from \"~/infrastructure/trpc/routers/sentence\";\r\n\r\n/**\r\n * This is the primary router for your server.\r\n *\r\n * All routers added in /api/routers should be manually added here.\r\n */\r\nexport const appRouter = createTRPCRouter({\r\n  user: userRouter,\r\n  speech: speechRouter,\r\n  project: projectRouter,\r\n  sentence: sentenceRouter,\r\n});\r\n\r\n// export type definition of API\r\nexport type AppRouter = typeof appRouter;\r\n\r\n/**\r\n * Create a server-side caller for the tRPC API.\r\n * @example\r\n * const trpc = createCaller(createContext);\r\n * const res = await trpc.post.all();\r\n *       ^? Post[]\r\n */\r\nexport const createCaller = createCallerFactory(appRouter);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAOO,MAAM,YAAY,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,MAAM,kJAAA,CAAA,aAAU;IAChB,QAAQ,oJAAA,CAAA,eAAY;IACpB,SAAS,qJAAA,CAAA,gBAAa;IACtB,UAAU,sJAAA,CAAA,iBAAc;AAC1B;AAYO,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/project/ideas-app/speech/speech-app/src/app/api/trpc/%5Btrpc%5D/route.ts"], "sourcesContent": ["import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from \"@trpc/server/adapters/fetch\";\nimport { type NextRequest } from \"next/server\";\n\nimport { env } from \"~/env\";\nimport { appRouter } from \"~/infrastructure/trpc/root\";\nimport { createTRPCContext } from \"~/infrastructure/trpc/trpc\";\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a HTTP request (e.g. when you make requests from Client Components).\n */\nconst createContext = async (req: NextRequest) => {\n  return createTRPCContext({\n    headers: req.headers,\n  });\n};\n\nconst handler = (req: NextRequest) =>\n  fetchRequestHandler({\n    endpoint: \"/api/trpc\",\n    req,\n    router: appRouter,\n    createContext: () => createContext(req),\n    onError:\n      env.NODE_ENV === \"development\"\n        ? ({ path, error }) => {\n            console.error(\n              `❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`,\n            );\n          }\n        : undefined,\n  });\n\nexport { handler as GET, handler as POS<PERSON> };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAGA;AACA;AACA;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,OAAO;IAC3B,OAAO,CAAA,GAAA,uIAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,SAAS,IAAI,OAAO;IACtB;AACF;AAEA,MAAM,UAAU,CAAC,MACf,CAAA,GAAA,+QAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,UAAU;QACV;QACA,QAAQ,uIAAA,CAAA,YAAS;QACjB,eAAe,IAAM,cAAc;QACnC,SACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBACb,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACd,QAAQ,KAAK,CACX,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,OAAO,EAAE;QAE/D,IACA;IACR", "debugId": null}}]}