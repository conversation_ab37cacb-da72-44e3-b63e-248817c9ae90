import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '../trpc';
import { sentenceRouter } from './sentence'; // Import the new sentence router
import { observable } from '@trpc/server/observable';
import { type VoiceGenerationQueue } from '@prisma/client'; // Import the new model
import EventEmitter, { on } from 'events';
import { ee } from '~/utils/pubsub';


export const speechRouter = createTRPCRouter({
  create: protectedProcedure // Changed to protectedProcedure
    .input(z.object({ userId: z.string(), text: z.string(), audioUrl: z.string().url() }))
    .mutation(async ({ input, ctx }) => {
      const speech = await ctx.createSpeechUseCase.execute(input);
      return speech;
    }),

  generateVoice: protectedProcedure
    .input(z.object({ sentenceId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const queueTask = await ctx.generateVoiceUseCase.execute(input.sentenceId, ctx.session.user.id);
      // TODO: Publish initial task status to the subscription
      // ee.emit('queueTaskUpdate', queueTask);
      return queueTask;
    }),
  generateBatchSentence: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const queueTasks = await ctx.generateVoiceUseCase.batchGenerate(input.projectId, ctx.session.user.id);
      return queueTasks;
    }),
  // Add a subscription for voice generation queue task updates
  onQueueTaskUpdate: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .subscription(async function* ({ input, signal, ctx }){
      console.log('event emitter', ee);
      for await (const [data] of on(ee, 'queueTaskUpdate', {
          // Passing the AbortSignal from the request automatically cancels the event emitter when the request is aborted
          signal: signal,
      })) {
        const post = data as VoiceGenerationQueue;
        console.log({post});
        const sentence = await ctx.sentenceRepository.findById(post.sentenceId)
        if (sentence?.projectId === input.projectId) {
          yield sentence;
        }
      }
    }),

  sentence: sentenceRouter, // Merge the sentence router
});