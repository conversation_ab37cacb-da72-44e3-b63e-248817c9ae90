import React, { useState, type ReactNode } from 'react';
import { BsChevronDown, BsChevronUp } from 'react-icons/bs';

interface CollapsePanelProps {
  header: string | ReactNode;
  children: ReactNode;
  initialOpen?: boolean;
  disabled?: boolean;
  className?: string;
}

const CollapsePanel: React.FC<CollapsePanelProps> = ({ header, children, initialOpen = false, disabled, className }) => {
  const [isOpen, setIsOpen] = useState(initialOpen);

  const toggleOpen = () => {
    disabled ? null : setIsOpen(!isOpen);
  };

  return (
    <div className={`rounded-md mb-1 ${className}`}>
      <button
        className="flex justify-between  items-center w-full p-2 text-left font-medium text-gray-700 hover:bg-gray-100 focus:outline-none border-b border-gray-200"
        onClick={toggleOpen}
      >
        {typeof header === 'string' ? <span>{header}</span> : header}
        {isOpen ? <BsChevronUp className="h-4 w-4 text-gray-500" /> : <BsChevronDown className="h-4 w-4 text-gray-500" />}
      </button>
      {isOpen && (
        <div className="p-4 border-b border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsePanel;