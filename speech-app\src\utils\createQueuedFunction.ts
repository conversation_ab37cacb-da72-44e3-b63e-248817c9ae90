type AsyncFunction<T extends unknown[], R> = (...args: T) => Promise<R>;

export function createQueuedFunction<T extends unknown[], R>(
  func: AsyncFunction<T, R>
): AsyncFunction<T, R> {
  const queue: Array<{ args: T; resolve: (value: R | PromiseLike<R>) => void; reject: (reason?: unknown) => void }> = [];
  let isProcessing = false;

  const processNext = async (): Promise<void> => {
    if (queue.length === 0) {
      isProcessing = false;
      return;
    }

    isProcessing = true;
    const { args, resolve, reject } = queue.shift()!; // Get the next task

    try {
      const result = await func(...args);
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      // Process the next task regardless of success or failure
      processNext().catch((err) => {
        console.error("Error processing queue:", err);
      });
    }
  };

  return (...args: T): Promise<R> => {
    return new Promise<R>((resolve, reject) => {
      queue.push({ args, resolve, reject });
      if (!isProcessing) {
        processNext().catch((err) => {
          console.error("Error starting queue processing:", err);
        });
      }
    });
  };
}