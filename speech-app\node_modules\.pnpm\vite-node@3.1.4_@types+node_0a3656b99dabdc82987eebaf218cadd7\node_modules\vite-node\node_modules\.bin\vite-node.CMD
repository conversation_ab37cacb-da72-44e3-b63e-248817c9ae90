@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vite-node@3.1.4_@types+node_0a3656b99dabdc82987eebaf218cadd7\node_modules\vite-node\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vite-node@3.1.4_@types+node_0a3656b99dabdc82987eebaf218cadd7\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vite-node@3.1.4_@types+node_0a3656b99dabdc82987eebaf218cadd7\node_modules\vite-node\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\vite-node@3.1.4_@types+node_0a3656b99dabdc82987eebaf218cadd7\node_modules;E:\project\ideas-app\speech\speech-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\vite-node.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\vite-node.mjs" %*
)
