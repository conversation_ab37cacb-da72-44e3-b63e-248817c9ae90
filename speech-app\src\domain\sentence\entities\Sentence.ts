import { BaseEntity } from "~/domain/shared/BaseEntity";
import type { VoiceGeneration } from "~/domain/voiceGeneration/entities/VoiceGeneration";

export class Sentence extends BaseEntity {
  text: string;
  order: number;
  tone?: string;
  emotion?: string;
  audioUrl?: string; // Deprecated, moved to VoiceGeneration
  audioDuration?: number; // Deprecated, moved to VoiceGeneration
  status: string;
  projectId: string;
  voiceGenerations: VoiceGeneration[];
  selectedVoiceGenerationId?: string;
  selectedVoiceGeneration?: VoiceGeneration;
  generateConfig: {
    voiceName: string;
    seed: number;
    model: string;
  };
 
  constructor(
    id: string,
    text: string,
    order: number,
    status: string,
    projectId: string,
    createdAt: Date,
    updatedAt: Date,
    tone?: string,
    emotion?: string,
    audioUrl?: string,
    audioDuration?: number,
    voiceGenerations: VoiceGeneration[] = [],
    selectedVoiceGenerationId?: string,
    selectedVoiceGeneration?: VoiceGeneration,
    generateConfig: { voiceName: string; seed: number; model: string; } = { voiceName: '', seed: 0, model: '' },
  ) {
    super(id, createdAt, updatedAt);
    this.text = text;
    this.order = order;
    this.tone = tone;
    this.emotion = emotion;
    this.audioUrl = audioUrl;
    this.audioDuration = audioDuration;
    this.status = status;
    this.projectId = projectId;
    this.voiceGenerations = voiceGenerations;
    this.selectedVoiceGenerationId = selectedVoiceGenerationId;
    this.selectedVoiceGeneration = selectedVoiceGeneration;
    this.generateConfig = generateConfig;
  }
 
  toResponseObject() {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      text: this.text,
      order: this.order,
      tone: this.tone,
      emotion: this.emotion,
      audioUrl: this.audioUrl,
      audioDuration: this.audioDuration,
      status: this.status,
      projectId: this.projectId,
      voiceGenerations: this.voiceGenerations.map(vg => vg.toResponseObject()),
      selectedVoiceGenerationId: this.selectedVoiceGenerationId,
      selectedVoiceGeneration: this.selectedVoiceGeneration?.toResponseObject(),
      generateConfig: this.generateConfig,
    };
  }
}