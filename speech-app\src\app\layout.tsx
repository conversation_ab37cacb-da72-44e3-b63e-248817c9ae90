import "~/styles/globals.css";

import { type <PERSON>adata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { TRPCReactProvider } from "~/infrastructure/trpc/react";
import { SessionProvider } from "next-auth/react";
// Removed Header import

export const metadata: Metadata = {
  title: "Create T3 App",
  description: "Generated by create-t3-app",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`}>
      <body>
        <SessionProvider>
          <TRPCReactProvider>
            {children}
          </TRPCReactProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
