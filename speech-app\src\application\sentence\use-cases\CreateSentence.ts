import type { ISentenceRepository } from "~/domain/sentence/ISentenceRepository";
import type { Sentence } from "~/domain/sentence/entities/Sentence";

interface CreateSentenceInput {
  projectId: string;
  text: string;
}

export class CreateSentence {
  constructor(private sentenceRepository: ISentenceRepository) {}

  async execute(input: CreateSentenceInput): Promise<Sentence> {
    // Use the repository's create method to handle atomic order calculation and creation
    const newSentence = await this.sentenceRepository.create(input.text, input.projectId);

    return newSentence;
  }
}