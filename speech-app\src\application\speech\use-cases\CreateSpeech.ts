import type { ISpeechRepository } from '../../../domain/speech/repositories/ISpeechRepository';
import { Speech } from '../../../domain/speech/entities/Speech';
import type { CreateSpeechDto } from '../dtos/CreateSpeechDto';
import type { SpeechResponseDto } from '../dtos/SpeechResponseDto';

export class CreateSpeech {
  constructor(private speechRepository: ISpeechRepository) {}

  public async execute(input: CreateSpeechDto): Promise<SpeechResponseDto> {
    const speech = Speech.create({
      userId: input.userId,
      text: input.text,
      audioUrl: input.audioUrl,
    });

    await this.speechRepository.save(speech);

    return {
      id: speech.id,
      userId: speech.userId,
      text: speech.text,
      audioUrl: speech.audioUrl,
      createdAt: speech.createdAt,
      updatedAt: speech.updatedAt,
    };
  }
}