import { NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { db } from "~/infrastructure/persistence/prisma/client";

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required." },
        { status: 400 },
      );
    }

    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists." },
        { status: 409 },
      );
    }

    const hashedPassword = await hash(password, 12);

    const newUser = await db.user.create({
      data: {
        name: email, // Using email as name for now, as it's a required field
        email,
        password: hashedPassword,
      },
    });

    return NextResponse.json(
      { message: "User registered successfully.", user: newUser },
      { status: 201 },
    );
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "Internal server error." },
      { status: 500 },
    );
  }
}