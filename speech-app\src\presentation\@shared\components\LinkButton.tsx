import Link from 'next/link';
import React from 'react';

interface LinkButtonProps extends React.ComponentProps<typeof Link> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

const LinkButton: React.FC<LinkButtonProps> = ({
  href,
  children,
  variant = 'primary',
  size = 'medium',
  disabled,
  className,
  ...props
}) => {
  const baseClasses = "font-semibold rounded transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-400/30"; // Adjusted rounded, transition, added focus ring
  const disabledClasses = "opacity-50 cursor-not-allowed";

  const variantClasses = {
    primary: "bg-sky-500 hover:bg-sky-600 text-white shadow-sm", // Updated primary color and added shadow
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-800 shadow-sm", // Updated secondary color and added shadow
    danger: "bg-rose-400 hover:bg-rose-500 text-white shadow-sm", // Updated danger color and added shadow
  };

  const sizeClasses = {
    small: "py-1.5 px-3 text-sm", // Adjusted padding
    medium: "py-2 px-4 text-base", // Adjusted padding
    large: "py-3 px-6 text-lg", // Adjusted padding
  };

  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${disabled ? disabledClasses : ''}
    ${className || ''}
  `.replace(/\s+/g, ' ').trim();

  return (
    <Link
      href={disabled ? '#' : href} // Prevent navigation when disabled
      className={buttonClasses}
      aria-disabled={disabled} // ARIA attribute for accessibility
      {...props}
    >
      {children}
    </Link>
  );
};

export default LinkButton;