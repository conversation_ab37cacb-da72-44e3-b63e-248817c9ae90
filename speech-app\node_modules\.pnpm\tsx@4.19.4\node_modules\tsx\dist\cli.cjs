"use strict";var $s=Object.defineProperty;var a=(t,e)=>$s(t,"name",{value:e,configurable:!0});var Et=require("node:os"),Ts=require("tty"),xs=require("esbuild"),Os=require("./package-BuXLp0ll.cjs"),he=require("./get-pipe-path-BoR10qr8.cjs"),Ru=require("node:url"),Ns=require("child_process"),z=require("path"),De=require("fs"),je=require("./node-features-roYmp9jK.cjs"),Hs=require("node:path"),Ls=require("events"),Ae=require("util"),Is=require("stream"),bu=require("os"),Ps=require("node:net"),pt=require("node:fs"),ks=require("./temporary-directory-B83uKxJF.cjs");require("module");const Ms="known-flag",Gs="unknown-flag",Ws="argument",{stringify:ye}=JSON,js=/\B([A-Z])/g,Us=a(t=>t.replace(js,"-$1").toLowerCase(),"v$1"),{hasOwnProperty:Ks}=Object.prototype,we=a((t,e)=>Ks.call(t,e),"w$2"),Vs=a(t=>Array.isArray(t),"L$2"),vu=a(t=>typeof t=="function"?[t,!1]:Vs(t)?[t[0],!0]:vu(t.type),"b$2"),zs=a((t,e)=>t===Boolean?e!=="false":e,"d$2"),Ys=a((t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),"m$1"),qs=/[\s.:=]/,Xs=a(t=>{const e=`Flag name ${ye(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(qs);if(u)throw new Error(`${e} cannot contain ${ye(u?.[0])}`)},"B"),Qs=a(t=>{const e={},u=a((r,s)=>{if(we(e,r))throw new Error(`Duplicate flags named ${ye(r)}`);e[r]=s},"r");for(const r in t){if(!we(t,r))continue;Xs(r);const s=t[r],n=[[],...vu(s),s];u(r,n);const i=Us(r);if(r!==i&&u(i,n),"alias"in s&&typeof s.alias=="string"){const{alias:D}=s,o=`Flag alias ${ye(D)} for flag ${ye(r)}`;if(D.length===0)throw new Error(`${o} cannot be empty`);if(D.length>1)throw new Error(`${o} must be a single character`);u(D,n)}}return e},"K$1"),Zs=a((t,e)=>{const u={};for(const r in t){if(!we(t,r))continue;const[s,,n,i]=e[r];if(s.length===0&&"default"in i){let{default:D}=i;typeof D=="function"&&(D=D()),u[r]=D}else u[r]=n?s:s.pop()}return u},"_$2"),Ue="--",Js=/[.:=]/,en=/^-{1,2}\w/,tn=a(t=>{if(!en.test(t))return;const e=!t.startsWith(Ue);let u=t.slice(e?1:2),r;const s=u.match(Js);if(s){const{index:n}=s;r=u.slice(n+1),u=u.slice(0,n)}return[u,r,e]},"N"),un=a((t,{onFlag:e,onArgument:u})=>{let r;const s=a((n,i)=>{if(typeof r!="function")return!0;r(n,i),r=void 0},"o");for(let n=0;n<t.length;n+=1){const i=t[n];if(i===Ue){s();const o=t.slice(n+1);u?.(o,[n],!0);break}const D=tn(i);if(D){if(s(),!e)continue;const[o,c,f]=D;if(f)for(let h=0;h<o.length;h+=1){s();const l=h===o.length-1;r=e(o[h],l?c:void 0,[n,h+1,l])}else r=e(o,c,[n])}else s(i,[n])&&u?.([i],[n])}s()},"$$1"),rn=a((t,e)=>{for(const[u,r,s]of e.reverse()){if(r){const n=t[u];let i=n.slice(0,r);if(s||(i+=n.slice(r+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},"E"),Su=a((t,e=process.argv.slice(2),{ignore:u}={})=>{const r=[],s=Qs(t),n={},i=[];return i[Ue]=[],un(e,{onFlag(D,o,c){const f=we(s,D);if(!u?.(f?Ms:Gs,D,o)){if(f){const[h,l]=s[D],p=zs(l,o),C=a((g,y)=>{r.push(c),y&&r.push(y),h.push(Ys(l,g||""))},"p");return p===void 0?C:C(p)}we(n,D)||(n[D]=[]),n[D].push(o===void 0?!0:o),r.push(c)}},onArgument(D,o,c){u?.(Ws,e[o[0]])||(i.push(...D),c?(i[Ue]=D,e.splice(o[0])):r.push(o))}}),rn(e,r),{flags:Zs(t,s),unknownFlags:n,_:i}},"U$2");var sn=Object.create,Ke=Object.defineProperty,nn=Object.defineProperties,Dn=Object.getOwnPropertyDescriptor,on=Object.getOwnPropertyDescriptors,an=Object.getOwnPropertyNames,Bu=Object.getOwnPropertySymbols,ln=Object.getPrototypeOf,$u=Object.prototype.hasOwnProperty,cn=Object.prototype.propertyIsEnumerable,Tu=a((t,e,u)=>e in t?Ke(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,"W$1"),Ve=a((t,e)=>{for(var u in e||(e={}))$u.call(e,u)&&Tu(t,u,e[u]);if(Bu)for(var u of Bu(e))cn.call(e,u)&&Tu(t,u,e[u]);return t},"p"),Ct=a((t,e)=>nn(t,on(e)),"c"),fn=a(t=>Ke(t,"__esModule",{value:!0}),"nD"),hn=a((t,e)=>()=>(t&&(e=t(t=0)),e),"rD"),dn=a((t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),"iD"),En=a((t,e,u,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of an(e))!$u.call(t,s)&&s!=="default"&&Ke(t,s,{get:a(()=>e[s],"get"),enumerable:!(r=Dn(e,s))||r.enumerable});return t},"oD"),pn=a((t,e)=>En(fn(Ke(t!=null?sn(ln(t)):{},"default",{value:t,enumerable:!0})),t),"BD"),K=hn(()=>{}),Cn=dn((t,e)=>{K(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});K(),K(),K();var Fn=a(t=>{var e,u,r;let s=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(s)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:s}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(r=t.stdoutColumns)!=null?r:s}},"v");K(),K(),K(),K(),K();function gn({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}a(gn,"w$1");function xu(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(gn(),"")}a(xu,"d$1"),K();function mn(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}a(mn,"y$1");var _n=pn(Cn());function oe(t){if(typeof t!="string"||t.length===0||(t=xu(t),t.length===0))return 0;t=t.replace((0,_n.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let r=t.codePointAt(u);r<=31||r>=127&&r<=159||r>=768&&r<=879||(r>65535&&u++,e+=mn(r)?2:1)}return e}a(oe,"g");var Ou=a(t=>Math.max(...t.split(`
`).map(oe)),"b$1"),An=a(t=>{let e=[];for(let u of t){let{length:r}=u,s=r-e.length;for(let n=0;n<s;n+=1)e.push(0);for(let n=0;n<r;n+=1){let i=Ou(u[n]);i>e[n]&&(e[n]=i)}}return e},"k$1");K();var Nu=/^\d+%$/,Hu={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},yn=a((t,e)=>{var u;let r=[];for(let s=0;s<t.length;s+=1){let n=(u=e[s])!=null?u:"auto";if(typeof n=="number"||n==="auto"||n==="content-width"||typeof n=="string"&&Nu.test(n)){r.push(Ct(Ve({},Hu),{width:n,contentWidth:t[s]}));continue}if(n&&typeof n=="object"){let i=Ct(Ve(Ve({},Hu),n),{contentWidth:t[s]});i.horizontalPadding=i.paddingLeft+i.paddingRight,r.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(n)}`)}return r},"sD");function wn(t,e){for(let u of t){let{width:r}=u;if(r==="content-width"&&(u.width=u.contentWidth),r==="auto"){let o=Math.min(20,u.contentWidth);u.width=o,u.autoOverflow=u.contentWidth-o}if(typeof r=="string"&&Nu.test(r)){let o=Number.parseFloat(r.slice(0,-1))/100;u.width=Math.floor(e*o)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:s}=u,n=1,i=n+s;if(i>=e){let o=i-e,c=Math.ceil(u.paddingLeft/s*o),f=o-c;u.paddingLeft-=c,u.paddingRight-=f,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let D=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,D),n)}}a(wn,"aD");var Lu=a(()=>Object.assign([],{columns:0}),"G$1");function Rn(t,e){let u=[Lu()],[r]=u;for(let s of t){let n=s.width+s.horizontalPadding;r.columns+n>e&&(r=Lu(),u.push(r)),r.push(s),r.columns+=n}for(let s of u){let n=s.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-n;if(i===0)continue;let D=s.filter(l=>"autoOverflow"in l),o=D.filter(l=>l.autoOverflow>0),c=o.reduce((l,p)=>l+p.autoOverflow,0),f=Math.min(c,i);for(let l of o){let p=Math.floor(l.autoOverflow/c*f);l.width+=p,i-=p}let h=Math.floor(i/D.length);for(let l=0;l<D.length;l+=1){let p=D[l];l===D.length-1?p.width+=i:p.width+=h,i-=h}}return u}a(Rn,"lD");function bn(t,e,u){let r=yn(u,e);return wn(r,t),Rn(r,t)}a(bn,"Z$1"),K(),K(),K();var Ft=10,Iu=a((t=0)=>e=>`\x1B[${e+t}m`,"U$1"),Pu=a((t=0)=>e=>`\x1B[${38+t};5;${e}m`,"V$1"),ku=a((t=0)=>(e,u,r)=>`\x1B[${38+t};2;${e};${u};${r}m`,"Y");function vn(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,r]of Object.entries(e)){for(let[s,n]of Object.entries(r))e[s]={open:`\x1B[${n[0]}m`,close:`\x1B[${n[1]}m`},r[s]=e[s],t.set(n[0],n[1]);Object.defineProperty(e,u,{value:r,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=Iu(),e.color.ansi256=Pu(),e.color.ansi16m=ku(),e.bgColor.ansi=Iu(Ft),e.bgColor.ansi256=Pu(Ft),e.bgColor.ansi16m=ku(Ft),Object.defineProperties(e,{rgbToAnsi256:{value:a((u,r,s)=>u===r&&r===s?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(r/255*5)+Math.round(s/255*5),"value"),enumerable:!1},hexToRgb:{value:a(u=>{let r=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!r)return[0,0,0];let{colorString:s}=r.groups;s.length===3&&(s=s.split("").map(i=>i+i).join(""));let n=Number.parseInt(s,16);return[n>>16&255,n>>8&255,n&255]},"value"),enumerable:!1},hexToAnsi256:{value:a(u=>e.rgbToAnsi256(...e.hexToRgb(u)),"value"),enumerable:!1},ansi256ToAnsi:{value:a(u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let r,s,n;if(u>=232)r=((u-232)*10+8)/255,s=r,n=r;else{u-=16;let o=u%36;r=Math.floor(u/36)/5,s=Math.floor(o/6)/5,n=o%6/5}let i=Math.max(r,s,n)*2;if(i===0)return 30;let D=30+(Math.round(n)<<2|Math.round(s)<<1|Math.round(r));return i===2&&(D+=60),D},"value"),enumerable:!1},rgbToAnsi:{value:a((u,r,s)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,r,s)),"value"),enumerable:!1},hexToAnsi:{value:a(u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),"value"),enumerable:!1}}),e}a(vn,"AD");var Sn=vn(),Bn=Sn,ze=new Set(["\x1B","\x9B"]),$n=39,gt="\x07",Mu="[",Tn="]",Gu="m",mt=`${Tn}8;;`,Wu=a(t=>`${ze.values().next().value}${Mu}${t}${Gu}`,"J$1"),ju=a(t=>`${ze.values().next().value}${mt}${t}${gt}`,"Q"),xn=a(t=>t.split(" ").map(e=>oe(e)),"hD"),_t=a((t,e,u)=>{let r=[...e],s=!1,n=!1,i=oe(xu(t[t.length-1]));for(let[D,o]of r.entries()){let c=oe(o);if(i+c<=u?t[t.length-1]+=o:(t.push(o),i=0),ze.has(o)&&(s=!0,n=r.slice(D+1).join("").startsWith(mt)),s){n?o===gt&&(s=!1,n=!1):o===Gu&&(s=!1);continue}i+=c,i===u&&D<r.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},"S$1"),On=a(t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(oe(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},"cD"),Nn=a((t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let r="",s,n,i=xn(t),D=[""];for(let[c,f]of t.split(" ").entries()){u.trim!==!1&&(D[D.length-1]=D[D.length-1].trimStart());let h=oe(D[D.length-1]);if(c!==0&&(h>=e&&(u.wordWrap===!1||u.trim===!1)&&(D.push(""),h=0),(h>0||u.trim===!1)&&(D[D.length-1]+=" ",h++)),u.hard&&i[c]>e){let l=e-h,p=1+Math.floor((i[c]-l-1)/e);Math.floor((i[c]-1)/e)<p&&D.push(""),_t(D,f,e);continue}if(h+i[c]>e&&h>0&&i[c]>0){if(u.wordWrap===!1&&h<e){_t(D,f,e);continue}D.push("")}if(h+i[c]>e&&u.wordWrap===!1){_t(D,f,e);continue}D[D.length-1]+=f}u.trim!==!1&&(D=D.map(c=>On(c)));let o=[...D.join(`
`)];for(let[c,f]of o.entries()){if(r+=f,ze.has(f)){let{groups:l}=new RegExp(`(?:\\${Mu}(?<code>\\d+)m|\\${mt}(?<uri>.*)${gt})`).exec(o.slice(c).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);s=p===$n?void 0:p}else l.uri!==void 0&&(n=l.uri.length===0?void 0:l.uri)}let h=Bn.codes.get(Number(s));o[c+1]===`
`?(n&&(r+=ju("")),s&&h&&(r+=Wu(h))):f===`
`&&(s&&h&&(r+=Wu(s)),n&&(r+=ju(n)))}return r},"dD");function Hn(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(r=>Nn(r,e,u)).join(`
`)}a(Hn,"T$1");var Uu=a(t=>Array.from({length:t}).fill(""),"X");function Ln(t,e){let u=[],r=0;for(let s of t){let n=0,i=s.map(o=>{var c;let f=(c=e[r])!=null?c:"";r+=1,o.preprocess&&(f=o.preprocess(f)),Ou(f)>o.width&&(f=Hn(f,o.width,{hard:!0}));let h=f.split(`
`);if(o.postprocess){let{postprocess:l}=o;h=h.map((p,C)=>l.call(o,p,C))}return o.paddingTop&&h.unshift(...Uu(o.paddingTop)),o.paddingBottom&&h.push(...Uu(o.paddingBottom)),h.length>n&&(n=h.length),Ct(Ve({},o),{lines:h})}),D=[];for(let o=0;o<n;o+=1){let c=i.map(f=>{var h;let l=(h=f.lines[o])!=null?h:"",p=Number.isFinite(f.width)?" ".repeat(f.width-oe(l)):"",C=f.paddingLeftString;return f.align==="right"&&(C+=p),C+=l,f.align==="left"&&(C+=p),C+f.paddingRightString}).join("");D.push(c)}u.push(D.join(`
`))}return u.join(`
`)}a(Ln,"P");function In(t,e){if(!t||t.length===0)return"";let u=An(t),r=u.length;if(r===0)return"";let{stdoutColumns:s,columns:n}=Fn(e);if(n.length>r)throw new Error(`${n.length} columns defined, but only ${r} columns found`);let i=bn(s,n,u);return t.map(D=>Ln(i,D)).join(`
`)}a(In,"mD"),K();var Pn=["<",">","=",">=","<="];function kn(t){if(!Pn.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}a(kn,"xD");function Mn(t){let e=Object.keys(t).map(u=>{let[r,s]=u.split(" ");kn(r);let n=Number.parseInt(s,10);if(Number.isNaN(n))throw new TypeError(`Invalid breakpoint value: ${s}`);let i=t[u];return{operator:r,breakpoint:n,value:i}}).sort((u,r)=>r.breakpoint-u.breakpoint);return u=>{var r;return(r=e.find(({operator:s,breakpoint:n})=>s==="="&&u===n||s===">"&&u>n||s==="<"&&u<n||s===">="&&u>=n||s==="<="&&u<=n))==null?void 0:r.value}}a(Mn,"wD");const Gn=a(t=>t.replace(/[\W_]([a-z\d])?/gi,(e,u)=>u?u.toUpperCase():""),"S"),Wn=a(t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),"q"),jn={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:a(t=>t.trim(),"preprocess")},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function Un(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((u,r)=>u.localeCompare(r)).map(u=>{const r=t[u],s="alias"in r;return s&&(e=!0),{name:u,flag:r,flagFormatted:`--${Wn(u)}`,aliasesEnabled:e,aliasFormatted:s?`-${r.alias}`:void 0}}).map(u=>(u.aliasesEnabled=e,[{type:"flagName",data:u},{type:"flagDescription",data:u}])),tableBreakpoints:jn}}}a(Un,"D");const Ku=a(t=>!t||(t.version??(t.help?t.help.version:void 0)),"A"),Vu=a(t=>{const e="parent"in t&&t.parent?.name;return(e?`${e} `:"")+t.name},"C");function Kn(t){const e=[];t.name&&e.push(Vu(t));const u=Ku(t)??("parent"in t&&Ku(t.parent));if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}a(Kn,"R");function Vn(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}a(Vn,"L");function zn(t){const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const u=[],r=[Vu(t)];if(t.flags&&Object.keys(t.flags).length>0&&r.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:s}=t,n=s.indexOf("--"),i=n>-1&&s.slice(n+1).some(D=>D.startsWith("<"));r.push(s.map(D=>D!=="--"?D:i?"--":"[--]").join(" "))}if(r.length>1&&u.push(r.join(" ")),"commands"in t&&t.commands?.length&&u.push(`${t.name} <command>`),u.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:u.join(`
`)}}}}a(zn,"T");function Yn(t){return!("commands"in t)||!t.commands?.length?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(e=>[e.options.name,e.options.help?e.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}a(Yn,"_");function qn(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:Un(t.flags),indentBody:0}}}a(qn,"k");function Xn(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}a(Xn,"F");function Qn(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}a(Qn,"H");const Zn=a(t=>[Kn,Vn,zn,Yn,qn,Xn,Qn].map(e=>e(t)).filter(Boolean),"U"),Jn=Ts.WriteStream.prototype.hasColors();class ei{static{a(this,"M")}text(e){return e}bold(e){return Jn?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:r=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:r}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:r}){return In(e.map(s=>s.map(n=>this.render(n))),r?Mn(r):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:r,aliasesEnabled:s,aliasFormatted:n}=e;let i="";if(n?i+=`${n}, `:s&&(i+="    "),i+=r,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const D=this.flagParameter("type"in u?u.type:u);D&&(i+=`${this.flagOperator(e)}${D}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){let u="description"in e?e.description??"":"";if("default"in e){let{default:r}=e;typeof r=="function"&&(r=r()),r&&(u+=` (default: ${this.flagDefault(r)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const At=/^[\w.-]+$/,{stringify:ee}=JSON,ti=/[|\\{}()[\]^$+*?.]/;function yt(t){const e=[];let u,r;for(const s of t){if(r)throw new Error(`Invalid parameter: Spread parameter ${ee(r)} must be last`);const n=s[0],i=s[s.length-1];let D;if(n==="<"&&i===">"&&(D=!0,u))throw new Error(`Invalid parameter: Required parameter ${ee(s)} cannot come after optional parameter ${ee(u)}`);if(n==="["&&i==="]"&&(D=!1,u=s),D===void 0)throw new Error(`Invalid parameter: ${ee(s)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let o=s.slice(1,-1);const c=o.slice(-3)==="...";c&&(r=s,o=o.slice(0,-3));const f=o.match(ti);if(f)throw new Error(`Invalid parameter: ${ee(s)}. Invalid character found ${ee(f[0])}`);e.push({name:o,required:D,spread:c})}return e}a(yt,"w");function wt(t,e,u,r){for(let s=0;s<e.length;s+=1){const{name:n,required:i,spread:D}=e[s],o=Gn(n);if(o in t)throw new Error(`Invalid parameter: ${ee(n)} is used more than once.`);const c=D?u.slice(s):u[s];if(D&&(s=e.length),i&&(!c||D&&c.length===0))return console.error(`Error: Missing required parameter ${ee(n)}
`),r(),process.exit(1);t[o]=c}}a(wt,"b");function ui(t){return t===void 0||t!==!1}a(ui,"W");function zu(t,e,u,r){const s={...e.flags},n=e.version;n&&(s.version={type:Boolean,description:"Show version"});const{help:i}=e,D=ui(i);D&&!("help"in s)&&(s.help={type:Boolean,alias:"h",description:"Show help"});const o=Su(s,r,{ignore:e.ignoreArgv}),c=a(()=>{console.log(e.version)},"f");if(n&&o.flags.version===!0)return c(),process.exit(0);const f=new ei,h=D&&i?.render?i.render:C=>f.render(C),l=a(C=>{const g=Zn({...e,...C?{help:C}:{},flags:s});console.log(h(g,f))},"u");if(D&&o.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,g=o._;const y=C.indexOf("--"),B=C.slice(y+1),H=Object.create(null);if(y>-1&&B.length>0){C=C.slice(0,y);const $=o._["--"];g=g.slice(0,-$.length||void 0),wt(H,yt(C),g,l),wt(H,yt(B),$,l)}else wt(H,yt(C),g,l);Object.assign(o._,H)}const p={...o,showVersion:c,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}a(zu,"x");function ri(t,e){const u=new Map;for(const r of e){const s=[r.options.name],{alias:n}=r.options;n&&(Array.isArray(n)?s.push(...n):s.push(n));for(const i of s){if(u.has(i))throw new Error(`Duplicate command name found: ${ee(i)}`);u.set(i,r)}}return u.get(t)}a(ri,"z");function Yu(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!At.test(t.name)))throw new Error(`Invalid script name: ${ee(t.name)}`);const r=u[0];if(t.commands&&At.test(r)){const s=ri(r,t.commands);if(s)return zu(s.options.name,{...s.options,parent:t},s.callback,u.slice(1))}return zu(void 0,t,e,u)}a(Yu,"Z");function si(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!At.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}a(si,"G");var ni=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ii(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}a(ii,"getDefaultExportFromCjs");var de={exports:{}},Rt,qu;function Di(){if(qu)return Rt;qu=1,Rt=r,r.sync=s;var t=De;function e(n,i){var D=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!D||(D=D.split(";"),D.indexOf("")!==-1))return!0;for(var o=0;o<D.length;o++){var c=D[o].toLowerCase();if(c&&n.substr(-c.length).toLowerCase()===c)return!0}return!1}a(e,"checkPathExt");function u(n,i,D){return!n.isSymbolicLink()&&!n.isFile()?!1:e(i,D)}a(u,"checkStat");function r(n,i,D){t.stat(n,function(o,c){D(o,o?!1:u(c,n,i))})}a(r,"isexe");function s(n,i){return u(t.statSync(n),n,i)}return a(s,"sync"),Rt}a(Di,"requireWindows");var bt,Xu;function oi(){if(Xu)return bt;Xu=1,bt=e,e.sync=u;var t=De;function e(n,i,D){t.stat(n,function(o,c){D(o,o?!1:r(c,i))})}a(e,"isexe");function u(n,i){return r(t.statSync(n),i)}a(u,"sync");function r(n,i){return n.isFile()&&s(n,i)}a(r,"checkStat");function s(n,i){var D=n.mode,o=n.uid,c=n.gid,f=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),h=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),g=l|p,y=D&C||D&p&&c===h||D&l&&o===f||D&g&&f===0;return y}return a(s,"checkMode"),bt}a(oi,"requireMode");var Ye;process.platform==="win32"||ni.TESTING_WINDOWS?Ye=Di():Ye=oi();var ai=vt;vt.sync=li;function vt(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(r,s){vt(t,e||{},function(n,i){n?s(n):r(i)})})}Ye(t,e||{},function(r,s){r&&(r.code==="EACCES"||e&&e.ignoreErrors)&&(r=null,s=!1),u(r,s)})}a(vt,"isexe$1");function li(t,e){try{return Ye.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}a(li,"sync");const Ee=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Qu=z,ci=Ee?";":":",Zu=ai,Ju=a(t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),"getNotFoundError"),er=a((t,e)=>{const u=e.colon||ci,r=t.match(/\//)||Ee&&t.match(/\\/)?[""]:[...Ee?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],s=Ee?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",n=Ee?s.split(u):[""];return Ee&&t.indexOf(".")!==-1&&n[0]!==""&&n.unshift(""),{pathEnv:r,pathExt:n,pathExtExe:s}},"getPathInfo"),tr=a((t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:r,pathExt:s,pathExtExe:n}=er(t,e),i=[],D=a(c=>new Promise((f,h)=>{if(c===r.length)return e.all&&i.length?f(i):h(Ju(t));const l=r[c],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=Qu.join(p,t),g=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;f(o(g,c,0))}),"step"),o=a((c,f,h)=>new Promise((l,p)=>{if(h===s.length)return l(D(f+1));const C=s[h];Zu(c+C,{pathExt:n},(g,y)=>{if(!g&&y)if(e.all)i.push(c+C);else return l(c+C);return l(o(c,f,h+1))})}),"subStep");return u?D(0).then(c=>u(null,c),u):D(0)},"which$1"),fi=a((t,e)=>{e=e||{};const{pathEnv:u,pathExt:r,pathExtExe:s}=er(t,e),n=[];for(let i=0;i<u.length;i++){const D=u[i],o=/^".*"$/.test(D)?D.slice(1,-1):D,c=Qu.join(o,t),f=!o&&/^\.[\\\/]/.test(t)?t.slice(0,2)+c:c;for(let h=0;h<r.length;h++){const l=f+r[h];try{if(Zu.sync(l,{pathExt:s}))if(e.all)n.push(l);else return l}catch{}}}if(e.all&&n.length)return n;if(e.nothrow)return null;throw Ju(t)},"whichSync");var hi=tr;tr.sync=fi;var St={exports:{}};const ur=a((t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(r=>r.toUpperCase()==="PATH")||"Path"},"pathKey");St.exports=ur,St.exports.default=ur;var di=St.exports;const rr=z,Ei=hi,pi=di;function sr(t,e){const u=t.options.env||process.env,r=process.cwd(),s=t.options.cwd!=null,n=s&&process.chdir!==void 0&&!process.chdir.disabled;if(n)try{process.chdir(t.options.cwd)}catch{}let i;try{i=Ei.sync(t.command,{path:u[pi({env:u})],pathExt:e?rr.delimiter:void 0})}catch{}finally{n&&process.chdir(r)}return i&&(i=rr.resolve(s?t.options.cwd:"",i)),i}a(sr,"resolveCommandAttempt");function Ci(t){return sr(t)||sr(t,!0)}a(Ci,"resolveCommand$1");var Fi=Ci,Bt={};const $t=/([()\][%!^"`<>&|;, *?])/g;function gi(t){return t=t.replace($t,"^$1"),t}a(gi,"escapeCommand");function mi(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace($t,"^$1"),e&&(t=t.replace($t,"^$1")),t}a(mi,"escapeArgument"),Bt.command=gi,Bt.argument=mi;var _i=/^#!(.*)/;const Ai=_i;var yi=a((t="")=>{const e=t.match(Ai);if(!e)return null;const[u,r]=e[0].replace(/#! ?/,"").split(" "),s=u.split("/").pop();return s==="env"?r:r?`${s} ${r}`:s},"shebangCommand$1");const Tt=De,wi=yi;function Ri(t){const u=Buffer.alloc(150);let r;try{r=Tt.openSync(t,"r"),Tt.readSync(r,u,0,150,0),Tt.closeSync(r)}catch{}return wi(u.toString())}a(Ri,"readShebang$1");var bi=Ri;const vi=z,nr=Fi,ir=Bt,Si=bi,Bi=process.platform==="win32",$i=/\.(?:com|exe)$/i,Ti=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function xi(t){t.file=nr(t);const e=t.file&&Si(t.file);return e?(t.args.unshift(t.file),t.command=e,nr(t)):t.file}a(xi,"detectShebang");function Oi(t){if(!Bi)return t;const e=xi(t),u=!$i.test(e);if(t.options.forceShell||u){const r=Ti.test(e);t.command=vi.normalize(t.command),t.command=ir.command(t.command),t.args=t.args.map(n=>ir.argument(n,r));const s=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${s}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}a(Oi,"parseNonShell");function Ni(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const r={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?r:Oi(r)}a(Ni,"parse$5");var Hi=Ni;const xt=process.platform==="win32";function Ot(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}a(Ot,"notFoundError");function Li(t,e){if(!xt)return;const u=t.emit;t.emit=function(r,s){if(r==="exit"){const n=Dr(s,e);if(n)return u.call(t,"error",n)}return u.apply(t,arguments)}}a(Li,"hookChildProcess");function Dr(t,e){return xt&&t===1&&!e.file?Ot(e.original,"spawn"):null}a(Dr,"verifyENOENT");function Ii(t,e){return xt&&t===1&&!e.file?Ot(e.original,"spawnSync"):null}a(Ii,"verifyENOENTSync");var Pi={hookChildProcess:Li,verifyENOENT:Dr,verifyENOENTSync:Ii,notFoundError:Ot};const or=Ns,Nt=Hi,Ht=Pi;function ar(t,e,u){const r=Nt(t,e,u),s=or.spawn(r.command,r.args,r.options);return Ht.hookChildProcess(s,r),s}a(ar,"spawn");function ki(t,e,u){const r=Nt(t,e,u),s=or.spawnSync(r.command,r.args,r.options);return s.error=s.error||Ht.verifyENOENTSync(s.status,r),s}a(ki,"spawnSync"),de.exports=ar,de.exports.spawn=ar,de.exports.sync=ki,de.exports._parse=Nt,de.exports._enoent=Ht;var Mi=de.exports,Gi=ii(Mi);const lr=a((t,e)=>{const u={...process.env},r=["inherit","inherit","inherit"];process.send&&r.push("ipc"),e&&(e.noCache&&(u.TSX_DISABLE_CACHE="1"),e.tsconfigPath&&(u.TSX_TSCONFIG_PATH=e.tsconfigPath));const s=t.filter(n=>n!=="-i"&&n!=="--interactive").length===0;return Gi(process.execPath,["--require",he.require.resolve("./preflight.cjs"),...s?["--require",he.require.resolve("./patch-repl.cjs")]:[],je.isFeatureSupported(je.moduleRegister)?"--import":"--loader",Ru.pathToFileURL(he.require.resolve("./loader.mjs")).toString(),...t],{stdio:r,env:u})},"run");var qe={};const Wi=z,te="\\\\/",cr=`[^${te}]`,ue="\\.",ji="\\+",Ui="\\?",Xe="\\/",Ki="(?=.)",fr="[^/]",Lt=`(?:${Xe}|$)`,hr=`(?:^|${Xe})`,It=`${ue}{1,2}${Lt}`,Vi=`(?!${ue})`,zi=`(?!${hr}${It})`,Yi=`(?!${ue}{0,1}${Lt})`,qi=`(?!${It})`,Xi=`[^.${Xe}]`,Qi=`${fr}*?`,dr={DOT_LITERAL:ue,PLUS_LITERAL:ji,QMARK_LITERAL:Ui,SLASH_LITERAL:Xe,ONE_CHAR:Ki,QMARK:fr,END_ANCHOR:Lt,DOTS_SLASH:It,NO_DOT:Vi,NO_DOTS:zi,NO_DOT_SLASH:Yi,NO_DOTS_SLASH:qi,QMARK_NO_DOT:Xi,STAR:Qi,START_ANCHOR:hr},Zi={...dr,SLASH_LITERAL:`[${te}]`,QMARK:cr,STAR:`${cr}*?`,DOTS_SLASH:`${ue}{1,2}(?:[${te}]|$)`,NO_DOT:`(?!${ue})`,NO_DOTS:`(?!(?:^|[${te}])${ue}{1,2}(?:[${te}]|$))`,NO_DOT_SLASH:`(?!${ue}{0,1}(?:[${te}]|$))`,NO_DOTS_SLASH:`(?!${ue}{1,2}(?:[${te}]|$))`,QMARK_NO_DOT:`[^.${te}]`,START_ANCHOR:`(?:^|[${te}])`,END_ANCHOR:`(?:[${te}]|$)`},Ji={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Qe={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Ji,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Wi.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?Zi:dr}};(function(t){const e=z,u=process.platform==="win32",{REGEX_BACKSLASH:r,REGEX_REMOVE_BACKSLASH:s,REGEX_SPECIAL_CHARS:n,REGEX_SPECIAL_CHARS_GLOBAL:i}=Qe;t.isObject=D=>D!==null&&typeof D=="object"&&!Array.isArray(D),t.hasRegexChars=D=>n.test(D),t.isRegexChar=D=>D.length===1&&t.hasRegexChars(D),t.escapeRegex=D=>D.replace(i,"\\$1"),t.toPosixSlashes=D=>D.replace(r,"/"),t.removeBackslashes=D=>D.replace(s,o=>o==="\\"?"":o),t.supportsLookbehinds=()=>{const D=process.version.slice(1).split(".").map(Number);return D.length===3&&D[0]>=9||D[0]===8&&D[1]>=10},t.isWindows=D=>D&&typeof D.windows=="boolean"?D.windows:u===!0||e.sep==="\\",t.escapeLast=(D,o,c)=>{const f=D.lastIndexOf(o,c);return f===-1?D:D[f-1]==="\\"?t.escapeLast(D,o,f-1):`${D.slice(0,f)}\\${D.slice(f)}`},t.removePrefix=(D,o={})=>{let c=D;return c.startsWith("./")&&(c=c.slice(2),o.prefix="./"),c},t.wrapOutput=(D,o={},c={})=>{const f=c.contains?"":"^",h=c.contains?"":"$";let l=`${f}(?:${D})${h}`;return o.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(qe);const Er=qe,{CHAR_ASTERISK:Pt,CHAR_AT:eD,CHAR_BACKWARD_SLASH:Re,CHAR_COMMA:tD,CHAR_DOT:kt,CHAR_EXCLAMATION_MARK:Mt,CHAR_FORWARD_SLASH:pr,CHAR_LEFT_CURLY_BRACE:Gt,CHAR_LEFT_PARENTHESES:Wt,CHAR_LEFT_SQUARE_BRACKET:uD,CHAR_PLUS:rD,CHAR_QUESTION_MARK:Cr,CHAR_RIGHT_CURLY_BRACE:sD,CHAR_RIGHT_PARENTHESES:Fr,CHAR_RIGHT_SQUARE_BRACKET:nD}=Qe,gr=a(t=>t===pr||t===Re,"isPathSeparator"),mr=a(t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},"depth"),iD=a((t,e)=>{const u=e||{},r=t.length-1,s=u.parts===!0||u.scanToEnd===!0,n=[],i=[],D=[];let o=t,c=-1,f=0,h=0,l=!1,p=!1,C=!1,g=!1,y=!1,B=!1,H=!1,$=!1,Q=!1,G=!1,se=0,W,A,v={value:"",depth:0,isGlob:!1};const M=a(()=>c>=r,"eos"),F=a(()=>o.charCodeAt(c+1),"peek"),O=a(()=>(W=A,o.charCodeAt(++c)),"advance");for(;c<r;){A=O();let j;if(A===Re){H=v.backslashes=!0,A=O(),A===Gt&&(B=!0);continue}if(B===!0||A===Gt){for(se++;M()!==!0&&(A=O());){if(A===Re){H=v.backslashes=!0,O();continue}if(A===Gt){se++;continue}if(B!==!0&&A===kt&&(A=O())===kt){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,s===!0)continue;break}if(B!==!0&&A===tD){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,s===!0)continue;break}if(A===sD&&(se--,se===0)){B=!1,l=v.isBrace=!0,G=!0;break}}if(s===!0)continue;break}if(A===pr){if(n.push(c),i.push(v),v={value:"",depth:0,isGlob:!1},G===!0)continue;if(W===kt&&c===f+1){f+=2;continue}h=c+1;continue}if(u.noext!==!0&&(A===rD||A===eD||A===Pt||A===Cr||A===Mt)===!0&&F()===Wt){if(C=v.isGlob=!0,g=v.isExtglob=!0,G=!0,A===Mt&&c===f&&(Q=!0),s===!0){for(;M()!==!0&&(A=O());){if(A===Re){H=v.backslashes=!0,A=O();continue}if(A===Fr){C=v.isGlob=!0,G=!0;break}}continue}break}if(A===Pt){if(W===Pt&&(y=v.isGlobstar=!0),C=v.isGlob=!0,G=!0,s===!0)continue;break}if(A===Cr){if(C=v.isGlob=!0,G=!0,s===!0)continue;break}if(A===uD){for(;M()!==!0&&(j=O());){if(j===Re){H=v.backslashes=!0,O();continue}if(j===nD){p=v.isBracket=!0,C=v.isGlob=!0,G=!0;break}}if(s===!0)continue;break}if(u.nonegate!==!0&&A===Mt&&c===f){$=v.negated=!0,f++;continue}if(u.noparen!==!0&&A===Wt){if(C=v.isGlob=!0,s===!0){for(;M()!==!0&&(A=O());){if(A===Wt){H=v.backslashes=!0,A=O();continue}if(A===Fr){G=!0;break}}continue}break}if(C===!0){if(G=!0,s===!0)continue;break}}u.noext===!0&&(g=!1,C=!1);let T=o,ne="",d="";f>0&&(ne=o.slice(0,f),o=o.slice(f),h-=f),T&&C===!0&&h>0?(T=o.slice(0,h),d=o.slice(h)):C===!0?(T="",d=o):T=o,T&&T!==""&&T!=="/"&&T!==o&&gr(T.charCodeAt(T.length-1))&&(T=T.slice(0,-1)),u.unescape===!0&&(d&&(d=Er.removeBackslashes(d)),T&&H===!0&&(T=Er.removeBackslashes(T)));const E={prefix:ne,input:t,start:f,base:T,glob:d,isBrace:l,isBracket:p,isGlob:C,isExtglob:g,isGlobstar:y,negated:$,negatedExtglob:Q};if(u.tokens===!0&&(E.maxDepth=0,gr(A)||i.push(v),E.tokens=i),u.parts===!0||u.tokens===!0){let j;for(let b=0;b<n.length;b++){const Z=j?j+1:f,J=n[b],V=t.slice(Z,J);u.tokens&&(b===0&&f!==0?(i[b].isPrefix=!0,i[b].value=ne):i[b].value=V,mr(i[b]),E.maxDepth+=i[b].depth),(b!==0||V!=="")&&D.push(V),j=J}if(j&&j+1<t.length){const b=t.slice(j+1);D.push(b),u.tokens&&(i[i.length-1].value=b,mr(i[i.length-1]),E.maxDepth+=i[i.length-1].depth)}E.slashes=n,E.parts=D}return E},"scan$1");var DD=iD;const Ze=Qe,Y=qe,{MAX_LENGTH:Je,POSIX_REGEX_SOURCE:oD,REGEX_NON_SPECIAL_CHARS:aD,REGEX_SPECIAL_CHARS_BACKREF:lD,REPLACEMENTS:_r}=Ze,cD=a((t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(s=>Y.escapeRegex(s)).join("..")}return u},"expandRange"),pe=a((t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,"syntaxError"),jt=a((t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=_r[t]||t;const u={...e},r=typeof u.maxLength=="number"?Math.min(Je,u.maxLength):Je;let s=t.length;if(s>r)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${r}`);const n={type:"bos",value:"",output:u.prepend||""},i=[n],D=u.capture?"":"?:",o=Y.isWindows(e),c=Ze.globChars(o),f=Ze.extglobChars(c),{DOT_LITERAL:h,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:g,NO_DOT:y,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:$,QMARK_NO_DOT:Q,STAR:G,START_ANCHOR:se}=c,W=a(_=>`(${D}(?:(?!${se}${_.dot?g:h}).)*?)`,"globstar"),A=u.dot?"":y,v=u.dot?$:Q;let M=u.bash===!0?W(u):G;u.capture&&(M=`(${M})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const F={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=Y.removePrefix(t,F),s=t.length;const O=[],T=[],ne=[];let d=n,E;const j=a(()=>F.index===s-1,"eos"),b=F.peek=(_=1)=>t[F.index+_],Z=F.advance=()=>t[++F.index]||"",J=a(()=>t.slice(F.index+1),"remaining"),V=a((_="",x=0)=>{F.consumed+=_,F.index+=x},"consume"),ke=a(_=>{F.output+=_.output!=null?_.output:_.value,V(_.value)},"append"),Ss=a(()=>{let _=1;for(;b()==="!"&&(b(2)!=="("||b(3)==="?");)Z(),F.start++,_++;return _%2===0?!1:(F.negated=!0,F.start++,!0)},"negate"),Me=a(_=>{F[_]++,ne.push(_)},"increment"),ie=a(_=>{F[_]--,ne.pop()},"decrement"),R=a(_=>{if(d.type==="globstar"){const x=F.braces>0&&(_.type==="comma"||_.type==="brace"),m=_.extglob===!0||O.length&&(_.type==="pipe"||_.type==="paren");_.type!=="slash"&&_.type!=="paren"&&!x&&!m&&(F.output=F.output.slice(0,-d.output.length),d.type="star",d.value="*",d.output=M,F.output+=d.output)}if(O.length&&_.type!=="paren"&&(O[O.length-1].inner+=_.value),(_.value||_.output)&&ke(_),d&&d.type==="text"&&_.type==="text"){d.value+=_.value,d.output=(d.output||"")+_.value;return}_.prev=d,i.push(_),d=_},"push"),Ge=a((_,x)=>{const m={...f[x],conditions:1,inner:""};m.prev=d,m.parens=F.parens,m.output=F.output;const w=(u.capture?"(":"")+m.open;Me("parens"),R({type:_,value:x,output:F.output?"":C}),R({type:"paren",extglob:!0,value:Z(),output:w}),O.push(m)},"extglobOpen"),Bs=a(_=>{let x=_.close+(u.capture?")":""),m;if(_.type==="negate"){let w=M;if(_.inner&&_.inner.length>1&&_.inner.includes("/")&&(w=W(u)),(w!==M||j()||/^\)+$/.test(J()))&&(x=_.close=`)$))${w}`),_.inner.includes("*")&&(m=J())&&/^\.[^\\/.]+$/.test(m)){const N=jt(m,{...e,fastpaths:!1}).output;x=_.close=`)${N})${w})`}_.prev.type==="bos"&&(F.negatedExtglob=!0)}R({type:"paren",extglob:!0,value:E,output:x}),ie("parens")},"extglobClose");if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let _=!1,x=t.replace(lD,(m,w,N,U,I,dt)=>U==="\\"?(_=!0,m):U==="?"?w?w+U+(I?$.repeat(I.length):""):dt===0?v+(I?$.repeat(I.length):""):$.repeat(N.length):U==="."?h.repeat(N.length):U==="*"?w?w+U+(I?M:""):M:w?m:`\\${m}`);return _===!0&&(u.unescape===!0?x=x.replace(/\\/g,""):x=x.replace(/\\+/g,m=>m.length%2===0?"\\\\":m?"\\":"")),x===t&&u.contains===!0?(F.output=t,F):(F.output=Y.wrapOutput(x,F,e),F)}for(;!j();){if(E=Z(),E==="\0")continue;if(E==="\\"){const m=b();if(m==="/"&&u.bash!==!0||m==="."||m===";")continue;if(!m){E+="\\",R({type:"text",value:E});continue}const w=/^\\+/.exec(J());let N=0;if(w&&w[0].length>2&&(N=w[0].length,F.index+=N,N%2!==0&&(E+="\\")),u.unescape===!0?E=Z():E+=Z(),F.brackets===0){R({type:"text",value:E});continue}}if(F.brackets>0&&(E!=="]"||d.value==="["||d.value==="[^")){if(u.posix!==!1&&E===":"){const m=d.value.slice(1);if(m.includes("[")&&(d.posix=!0,m.includes(":"))){const w=d.value.lastIndexOf("["),N=d.value.slice(0,w),U=d.value.slice(w+2),I=oD[U];if(I){d.value=N+I,F.backtrack=!0,Z(),!n.output&&i.indexOf(d)===1&&(n.output=C);continue}}}(E==="["&&b()!==":"||E==="-"&&b()==="]")&&(E=`\\${E}`),E==="]"&&(d.value==="["||d.value==="[^")&&(E=`\\${E}`),u.posix===!0&&E==="!"&&d.value==="["&&(E="^"),d.value+=E,ke({value:E});continue}if(F.quotes===1&&E!=='"'){E=Y.escapeRegex(E),d.value+=E,ke({value:E});continue}if(E==='"'){F.quotes=F.quotes===1?0:1,u.keepQuotes===!0&&R({type:"text",value:E});continue}if(E==="("){Me("parens"),R({type:"paren",value:E});continue}if(E===")"){if(F.parens===0&&u.strictBrackets===!0)throw new SyntaxError(pe("opening","("));const m=O[O.length-1];if(m&&F.parens===m.parens+1){Bs(O.pop());continue}R({type:"paren",value:E,output:F.parens?")":"\\)"}),ie("parens");continue}if(E==="["){if(u.nobracket===!0||!J().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(pe("closing","]"));E=`\\${E}`}else Me("brackets");R({type:"bracket",value:E});continue}if(E==="]"){if(u.nobracket===!0||d&&d.type==="bracket"&&d.value.length===1){R({type:"text",value:E,output:`\\${E}`});continue}if(F.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(pe("opening","["));R({type:"text",value:E,output:`\\${E}`});continue}ie("brackets");const m=d.value.slice(1);if(d.posix!==!0&&m[0]==="^"&&!m.includes("/")&&(E=`/${E}`),d.value+=E,ke({value:E}),u.literalBrackets===!1||Y.hasRegexChars(m))continue;const w=Y.escapeRegex(d.value);if(F.output=F.output.slice(0,-d.value.length),u.literalBrackets===!0){F.output+=w,d.value=w;continue}d.value=`(${D}${w}|${d.value})`,F.output+=d.value;continue}if(E==="{"&&u.nobrace!==!0){Me("braces");const m={type:"brace",value:E,output:"(",outputIndex:F.output.length,tokensIndex:F.tokens.length};T.push(m),R(m);continue}if(E==="}"){const m=T[T.length-1];if(u.nobrace===!0||!m){R({type:"text",value:E,output:E});continue}let w=")";if(m.dots===!0){const N=i.slice(),U=[];for(let I=N.length-1;I>=0&&(i.pop(),N[I].type!=="brace");I--)N[I].type!=="dots"&&U.unshift(N[I].value);w=cD(U,u),F.backtrack=!0}if(m.comma!==!0&&m.dots!==!0){const N=F.output.slice(0,m.outputIndex),U=F.tokens.slice(m.tokensIndex);m.value=m.output="\\{",E=w="\\}",F.output=N;for(const I of U)F.output+=I.output||I.value}R({type:"brace",value:E,output:w}),ie("braces"),T.pop();continue}if(E==="|"){O.length>0&&O[O.length-1].conditions++,R({type:"text",value:E});continue}if(E===","){let m=E;const w=T[T.length-1];w&&ne[ne.length-1]==="braces"&&(w.comma=!0,m="|"),R({type:"comma",value:E,output:m});continue}if(E==="/"){if(d.type==="dot"&&F.index===F.start+1){F.start=F.index+1,F.consumed="",F.output="",i.pop(),d=n;continue}R({type:"slash",value:E,output:p});continue}if(E==="."){if(F.braces>0&&d.type==="dot"){d.value==="."&&(d.output=h);const m=T[T.length-1];d.type="dots",d.output+=E,d.value+=E,m.dots=!0;continue}if(F.braces+F.parens===0&&d.type!=="bos"&&d.type!=="slash"){R({type:"text",value:E,output:h});continue}R({type:"dot",value:E,output:h});continue}if(E==="?"){if(!(d&&d.value==="(")&&u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Ge("qmark",E);continue}if(d&&d.type==="paren"){const w=b();let N=E;if(w==="<"&&!Y.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(d.value==="("&&!/[!=<:]/.test(w)||w==="<"&&!/<([!=]|\w+>)/.test(J()))&&(N=`\\${E}`),R({type:"text",value:E,output:N});continue}if(u.dot!==!0&&(d.type==="slash"||d.type==="bos")){R({type:"qmark",value:E,output:Q});continue}R({type:"qmark",value:E,output:$});continue}if(E==="!"){if(u.noextglob!==!0&&b()==="("&&(b(2)!=="?"||!/[!=<:]/.test(b(3)))){Ge("negate",E);continue}if(u.nonegate!==!0&&F.index===0){Ss();continue}}if(E==="+"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Ge("plus",E);continue}if(d&&d.value==="("||u.regex===!1){R({type:"plus",value:E,output:l});continue}if(d&&(d.type==="bracket"||d.type==="paren"||d.type==="brace")||F.parens>0){R({type:"plus",value:E});continue}R({type:"plus",value:l});continue}if(E==="@"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){R({type:"at",extglob:!0,value:E,output:""});continue}R({type:"text",value:E});continue}if(E!=="*"){(E==="$"||E==="^")&&(E=`\\${E}`);const m=aD.exec(J());m&&(E+=m[0],F.index+=m[0].length),R({type:"text",value:E});continue}if(d&&(d.type==="globstar"||d.star===!0)){d.type="star",d.star=!0,d.value+=E,d.output=M,F.backtrack=!0,F.globstar=!0,V(E);continue}let _=J();if(u.noextglob!==!0&&/^\([^?]/.test(_)){Ge("star",E);continue}if(d.type==="star"){if(u.noglobstar===!0){V(E);continue}const m=d.prev,w=m.prev,N=m.type==="slash"||m.type==="bos",U=w&&(w.type==="star"||w.type==="globstar");if(u.bash===!0&&(!N||_[0]&&_[0]!=="/")){R({type:"star",value:E,output:""});continue}const I=F.braces>0&&(m.type==="comma"||m.type==="brace"),dt=O.length&&(m.type==="pipe"||m.type==="paren");if(!N&&m.type!=="paren"&&!I&&!dt){R({type:"star",value:E,output:""});continue}for(;_.slice(0,3)==="/**";){const We=t[F.index+4];if(We&&We!=="/")break;_=_.slice(3),V("/**",3)}if(m.type==="bos"&&j()){d.type="globstar",d.value+=E,d.output=W(u),F.output=d.output,F.globstar=!0,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&!U&&j()){F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=W(u)+(u.strictSlashes?")":"|$)"),d.value+=E,F.globstar=!0,F.output+=m.output+d.output,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&_[0]==="/"){const We=_[1]!==void 0?"|$":"";F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=`${W(u)}${p}|${p}${We})`,d.value+=E,F.output+=m.output+d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}if(m.type==="bos"&&_[0]==="/"){d.type="globstar",d.value+=E,d.output=`(?:^|${p}|${W(u)}${p})`,F.output=d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}F.output=F.output.slice(0,-d.output.length),d.type="globstar",d.output=W(u),d.value+=E,F.output+=d.output,F.globstar=!0,V(E);continue}const x={type:"star",value:E,output:M};if(u.bash===!0){x.output=".*?",(d.type==="bos"||d.type==="slash")&&(x.output=A+x.output),R(x);continue}if(d&&(d.type==="bracket"||d.type==="paren")&&u.regex===!0){x.output=E,R(x);continue}(F.index===F.start||d.type==="slash"||d.type==="dot")&&(d.type==="dot"?(F.output+=B,d.output+=B):u.dot===!0?(F.output+=H,d.output+=H):(F.output+=A,d.output+=A),b()!=="*"&&(F.output+=C,d.output+=C)),R(x)}for(;F.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing","]"));F.output=Y.escapeLast(F.output,"["),ie("brackets")}for(;F.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing",")"));F.output=Y.escapeLast(F.output,"("),ie("parens")}for(;F.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(pe("closing","}"));F.output=Y.escapeLast(F.output,"{"),ie("braces")}if(u.strictSlashes!==!0&&(d.type==="star"||d.type==="bracket")&&R({type:"maybe_slash",value:"",output:`${p}?`}),F.backtrack===!0){F.output="";for(const _ of F.tokens)F.output+=_.output!=null?_.output:_.value,_.suffix&&(F.output+=_.suffix)}return F},"parse$3");jt.fastpaths=(t,e)=>{const u={...e},r=typeof u.maxLength=="number"?Math.min(Je,u.maxLength):Je,s=t.length;if(s>r)throw new SyntaxError(`Input length: ${s}, exceeds maximum allowed length: ${r}`);t=_r[t]||t;const n=Y.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:D,ONE_CHAR:o,DOTS_SLASH:c,NO_DOT:f,NO_DOTS:h,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=Ze.globChars(n),g=u.dot?h:f,y=u.dot?l:f,B=u.capture?"":"?:",H={negated:!1,prefix:""};let $=u.bash===!0?".*?":p;u.capture&&($=`(${$})`);const Q=a(A=>A.noglobstar===!0?$:`(${B}(?:(?!${C}${A.dot?c:i}).)*?)`,"globstar"),G=a(A=>{switch(A){case"*":return`${g}${o}${$}`;case".*":return`${i}${o}${$}`;case"*.*":return`${g}${$}${i}${o}${$}`;case"*/*":return`${g}${$}${D}${o}${y}${$}`;case"**":return g+Q(u);case"**/*":return`(?:${g}${Q(u)}${D})?${y}${o}${$}`;case"**/*.*":return`(?:${g}${Q(u)}${D})?${y}${$}${i}${o}${$}`;case"**/.*":return`(?:${g}${Q(u)}${D})?${i}${o}${$}`;default:{const v=/^(.*?)\.(\w+)$/.exec(A);if(!v)return;const M=G(v[1]);return M?M+i+v[2]:void 0}}},"create"),se=Y.removePrefix(t,H);let W=G(se);return W&&u.strictSlashes!==!0&&(W+=`${D}?`),W};var fD=jt;const hD=z,dD=DD,Ut=fD,Kt=qe,ED=Qe,pD=a(t=>t&&typeof t=="object"&&!Array.isArray(t),"isObject$1"),L=a((t,e,u=!1)=>{if(Array.isArray(t)){const f=t.map(l=>L(l,e,u));return a(l=>{for(const p of f){const C=p(l);if(C)return C}return!1},"arrayMatcher")}const r=pD(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!r)throw new TypeError("Expected pattern to be a non-empty string");const s=e||{},n=Kt.isWindows(e),i=r?L.compileRe(t,e):L.makeRe(t,e,!1,!0),D=i.state;delete i.state;let o=a(()=>!1,"isIgnored");if(s.ignore){const f={...e,ignore:null,onMatch:null,onResult:null};o=L(s.ignore,f,u)}const c=a((f,h=!1)=>{const{isMatch:l,match:p,output:C}=L.test(f,i,e,{glob:t,posix:n}),g={glob:t,state:D,regex:i,posix:n,input:f,output:C,match:p,isMatch:l};return typeof s.onResult=="function"&&s.onResult(g),l===!1?(g.isMatch=!1,h?g:!1):o(f)?(typeof s.onIgnore=="function"&&s.onIgnore(g),g.isMatch=!1,h?g:!1):(typeof s.onMatch=="function"&&s.onMatch(g),h?g:!0)},"matcher");return u&&(c.state=D),c},"picomatch$3");L.test=(t,e,u,{glob:r,posix:s}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const n=u||{},i=n.format||(s?Kt.toPosixSlashes:null);let D=t===r,o=D&&i?i(t):t;return D===!1&&(o=i?i(t):t,D=o===r),(D===!1||n.capture===!0)&&(n.matchBase===!0||n.basename===!0?D=L.matchBase(t,e,u,s):D=e.exec(o)),{isMatch:!!D,match:D,output:o}},L.matchBase=(t,e,u,r=Kt.isWindows(u))=>(e instanceof RegExp?e:L.makeRe(e,u)).test(hD.basename(t)),L.isMatch=(t,e,u)=>L(e,u)(t),L.parse=(t,e)=>Array.isArray(t)?t.map(u=>L.parse(u,e)):Ut(t,{...e,fastpaths:!1}),L.scan=(t,e)=>dD(t,e),L.compileRe=(t,e,u=!1,r=!1)=>{if(u===!0)return t.output;const s=e||{},n=s.contains?"":"^",i=s.contains?"":"$";let D=`${n}(?:${t.output})${i}`;t&&t.negated===!0&&(D=`^(?!${D}).*$`);const o=L.toRegex(D,e);return r===!0&&(o.state=t),o},L.makeRe=(t,e={},u=!1,r=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let s={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(s.output=Ut.fastpaths(t,e)),s.output||(s=Ut(t,e)),L.compileRe(s,e,u,r)},L.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},L.constants=ED;var CD=L,Ar=CD;const be=De,{Readable:FD}=Is,ve=z,{promisify:et}=Ae,Vt=Ar,gD=et(be.readdir),mD=et(be.stat),yr=et(be.lstat),_D=et(be.realpath),AD="!",wr="READDIRP_RECURSIVE_ERROR",yD=new Set(["ENOENT","EPERM","EACCES","ELOOP",wr]),zt="files",Rr="directories",tt="files_directories",ut="all",br=[zt,Rr,tt,ut],wD=a(t=>yD.has(t.code),"isNormalFlowError"),[vr,RD]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),bD=process.platform==="win32"&&(vr>10||vr===10&&RD>=5),Sr=a(t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=Vt(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const r of t){const s=r.trim();s.charAt(0)===AD?u.push(Vt(s.slice(1))):e.push(Vt(s))}return u.length>0?e.length>0?r=>e.some(s=>s(r.basename))&&!u.some(s=>s(r.basename)):r=>!u.some(s=>s(r.basename)):r=>e.some(s=>s(r.basename))}}},"normalizeFilter");class ht extends FD{static{a(this,"ReaddirpStream")}static get defaultOptions(){return{root:".",fileFilter:a(e=>!0,"fileFilter"),directoryFilter:a(e=>!0,"directoryFilter"),type:zt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...ht.defaultOptions,...e},{root:r,type:s}=u;this._fileFilter=Sr(u.fileFilter),this._directoryFilter=Sr(u.directoryFilter);const n=u.lstat?yr:mD;bD?this._stat=i=>n(i,{bigint:!0}):this._stat=n,this._maxDepth=u.depth,this._wantsDir=[Rr,tt,ut].includes(s),this._wantsFile=[zt,tt,ut].includes(s),this._wantsEverything=s===ut,this._root=ve.resolve(r),this._isDirent="Dirent"in be&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(r,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:r,files:s=[]}=this.parent||{};if(s.length>0){const n=s.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(n)){if(this.destroyed)return;const D=await this._getEntryType(i);D==="directory"&&this._directoryFilter(i)?(r<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,r+1)),this._wantsDir&&(this.push(i),e--)):(D==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const n=this.parents.pop();if(!n){this.push(null);break}if(this.parent=await n,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let r;try{r=await gD(e,this._rdOptions)}catch(s){this._onError(s)}return{files:r,depth:u,path:e}}async _formatEntry(e,u){let r;try{const s=this._isDirent?e.name:e,n=ve.resolve(ve.join(u,s));r={path:ve.relative(this._root,n),fullPath:n,basename:s},r[this._statsProp]=this._isDirent?e:await this._stat(n)}catch(s){this._onError(s)}return r}_onError(e){wD(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const r=e.fullPath;try{const s=await _D(r),n=await yr(s);if(n.isFile())return"file";if(n.isDirectory()){const i=s.length;if(r.startsWith(s)&&r.substr(i,1)===ve.sep){const D=new Error(`Circular symlink detected: "${r}" points to "${s}"`);return D.code=wr,this._onError(D)}return"directory"}}catch(s){this._onError(s)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const Ce=a((t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=tt),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!br.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${br.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new ht(e)},"readdirp$1"),vD=a((t,e={})=>new Promise((u,r)=>{const s=[];Ce(t,e).on("data",n=>s.push(n)).on("end",()=>u(s)).on("error",n=>r(n))}),"readdirpPromise");Ce.promise=vD,Ce.ReaddirpStream=ht,Ce.default=Ce;var SD=Ce,Yt={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var Br=a(function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var r="";if(u>4&&t[3]==="\\"){var s=t[2];(s==="?"||s===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),r="//")}var n=t.split(/[/\\]+/);return e!==!1&&n[n.length-1]===""&&n.pop(),r+n.join("/")},"normalizePath$2"),BD=Yt.exports;Object.defineProperty(BD,"__esModule",{value:!0});const $r=Ar,$D=Br,Tr="!",TD={returnIndex:!1},xD=a(t=>Array.isArray(t)?t:[t],"arrify$1"),OD=a((t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=$r(t,e);return r=>t===r||u(r)}return t instanceof RegExp?u=>t.test(u):u=>!1},"createPattern"),xr=a((t,e,u,r)=>{const s=Array.isArray(u),n=s?u[0]:u;if(!s&&typeof n!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(n));const i=$D(n,!1);for(let o=0;o<e.length;o++){const c=e[o];if(c(i))return r?-1:!1}const D=s&&[i].concat(u.slice(1));for(let o=0;o<t.length;o++){const c=t[o];if(s?c(...D):c(i))return r?o:!0}return r?-1:!1},"matchPatterns"),qt=a((t,e,u=TD)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const r=typeof u=="boolean"?{returnIndex:u}:u,s=r.returnIndex||!1,n=xD(t),i=n.filter(o=>typeof o=="string"&&o.charAt(0)===Tr).map(o=>o.slice(1)).map(o=>$r(o,r)),D=n.filter(o=>typeof o!="string"||typeof o=="string"&&o.charAt(0)!==Tr).map(o=>OD(o,r));return e==null?(o,c=!1)=>xr(D,i,o,typeof c=="boolean"?c:!1):xr(D,i,e,s)},"anymatch$1");qt.default=qt,Yt.exports=qt;var ND=Yt.exports;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var HD=a(function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1},"isExtglob");/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var LD=HD,Or={"{":"}","(":")","[":"]"},ID=a(function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,r=-2,s=-2,n=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||r!==-1&&t[e]==="["&&t[e+1]!=="]"&&(r<e&&(r=t.indexOf("]",e)),r>e&&(i===-1||i>r||(i=t.indexOf("\\",e),i===-1||i>r)))||s!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(s=t.indexOf("}",e),s>e&&(i=t.indexOf("\\",e),i===-1||i>s))||n!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(n=t.indexOf(")",e),n>e&&(i=t.indexOf("\\",e),i===-1||i>n))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(n=t.indexOf(")",u),n>u&&(i=t.indexOf("\\",u),i===-1||i>n))))return!0;if(t[e]==="\\"){var D=t[e+1];e+=2;var o=Or[D];if(o){var c=t.indexOf(o,e);c!==-1&&(e=c+1)}if(t[e]==="!")return!0}else e++}return!1},"strictCheck"),PD=a(function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var r=Or[u];if(r){var s=t.indexOf(r,e);s!==-1&&(e=s+1)}if(t[e]==="!")return!0}else e++}return!1},"relaxedCheck"),Nr=a(function(e,u){if(typeof e!="string"||e==="")return!1;if(LD(e))return!0;var r=ID;return u&&u.strict===!1&&(r=PD),r(e)},"isGlob"),kD=Nr,MD=z.posix.dirname,GD=bu.platform()==="win32",Xt="/",WD=/\\/g,jD=/[\{\[].*[\}\]]$/,UD=/(^|[^\\])([\{\[]|\([^\)]+$)/,KD=/\\([\!\*\?\|\[\]\(\)\{\}])/g,VD=a(function(e,u){var r=Object.assign({flipBackslashes:!0},u);r.flipBackslashes&&GD&&e.indexOf(Xt)<0&&(e=e.replace(WD,Xt)),jD.test(e)&&(e+=Xt),e+="a";do e=MD(e);while(kD(e)||UD.test(e));return e.replace(KD,"$1")},"globParent"),rt={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(r=>r.type===u),t.exceedsLimit=(e,u,r=1,s)=>s===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(r)>=s,t.escapeNode=(e,u=0,r)=>{let s=e.nodes[u];s&&(r&&s.type===r||s.type==="open"||s.type==="close")&&s.escaped!==!0&&(s.value="\\"+s.value,s.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"||e.commas>>0+e.ranges>>0?!1:(e.invalid=!0,!0),t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:!(e.commas>>0+e.ranges>>0)||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,r)=>(r.type==="text"&&u.push(r.value),r.type==="range"&&(r.type="text"),u),[]),t.flatten=(...e)=>{const u=[],r=a(s=>{for(let n=0;n<s.length;n++){let i=s[n];Array.isArray(i)?r(i):i!==void 0&&u.push(i)}return u},"flat");return r(e),u}})(rt);const Hr=rt;var Qt=a((t,e={})=>{let u=a((r,s={})=>{let n=e.escapeInvalid&&Hr.isInvalidBrace(s),i=r.invalid===!0&&e.escapeInvalid===!0,D="";if(r.value)return(n||i)&&Hr.isOpenOrClose(r)?"\\"+r.value:r.value;if(r.value)return r.value;if(r.nodes)for(let o of r.nodes)D+=u(o);return D},"stringify");return u(t)},"stringify$4");/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var zD=a(function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1},"isNumber$2");/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const Lr=zD,ae=a((t,e,u)=>{if(Lr(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(Lr(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let r={relaxZeros:!0,...u};typeof r.strictZeros=="boolean"&&(r.relaxZeros=r.strictZeros===!1);let s=String(r.relaxZeros),n=String(r.shorthand),i=String(r.capture),D=String(r.wrap),o=t+":"+e+"="+s+n+i+D;if(ae.cache.hasOwnProperty(o))return ae.cache[o].result;let c=Math.min(t,e),f=Math.max(t,e);if(Math.abs(c-f)===1){let g=t+"|"+e;return r.capture?`(${g})`:r.wrap===!1?g:`(?:${g})`}let h=Wr(t)||Wr(e),l={min:t,max:e,a:c,b:f},p=[],C=[];if(h&&(l.isPadded=h,l.maxLen=String(l.max).length),c<0){let g=f<0?Math.abs(f):1;C=Ir(g,Math.abs(c),l,r),c=l.a=0}return f>=0&&(p=Ir(c,f,l,r)),l.negatives=C,l.positives=p,l.result=YD(C,p),r.capture===!0?l.result=`(${l.result})`:r.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),ae.cache[o]=l,l.result},"toRegexRange$1");function YD(t,e,u){let r=Zt(t,e,"-",!1)||[],s=Zt(e,t,"",!1)||[],n=Zt(t,e,"-?",!0)||[];return r.concat(n).concat(s).join("|")}a(YD,"collatePatterns");function qD(t,e){let u=1,r=1,s=kr(t,u),n=new Set([e]);for(;t<=s&&s<=e;)n.add(s),u+=1,s=kr(t,u);for(s=Mr(e+1,r)-1;t<s&&s<=e;)n.add(s),r+=1,s=Mr(e+1,r)-1;return n=[...n],n.sort(ZD),n}a(qD,"splitToRanges");function XD(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let r=QD(t,e),s=r.length,n="",i=0;for(let D=0;D<s;D++){let[o,c]=r[D];o===c?n+=o:o!=="0"||c!=="9"?n+=JD(o,c):i++}return i&&(n+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:n,count:[i],digits:s}}a(XD,"rangeToPattern");function Ir(t,e,u,r){let s=qD(t,e),n=[],i=t,D;for(let o=0;o<s.length;o++){let c=s[o],f=XD(String(i),String(c),r),h="";if(!u.isPadded&&D&&D.pattern===f.pattern){D.count.length>1&&D.count.pop(),D.count.push(f.count[0]),D.string=D.pattern+Gr(D.count),i=c+1;continue}u.isPadded&&(h=eo(c,u,r)),f.string=h+f.pattern+Gr(f.count),n.push(f),i=c+1,D=f}return n}a(Ir,"splitToPatterns");function Zt(t,e,u,r,s){let n=[];for(let i of t){let{string:D}=i;!r&&!Pr(e,"string",D)&&n.push(u+D),r&&Pr(e,"string",D)&&n.push(u+D)}return n}a(Zt,"filterPatterns");function QD(t,e){let u=[];for(let r=0;r<t.length;r++)u.push([t[r],e[r]]);return u}a(QD,"zip");function ZD(t,e){return t>e?1:e>t?-1:0}a(ZD,"compare");function Pr(t,e,u){return t.some(r=>r[e]===u)}a(Pr,"contains");function kr(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}a(kr,"countNines");function Mr(t,e){return t-t%Math.pow(10,e)}a(Mr,"countZeros");function Gr(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}a(Gr,"toQuantifier");function JD(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}a(JD,"toCharacterClass");function Wr(t){return/^-?(0+)\d/.test(t)}a(Wr,"hasPadding");function eo(t,e,u){if(!e.isPadded)return t;let r=Math.abs(e.maxLen-String(t).length),s=u.relaxZeros!==!1;switch(r){case 0:return"";case 1:return s?"0?":"0";case 2:return s?"0{0,2}":"00";default:return s?`0{0,${r}}`:`0{${r}}`}}a(eo,"padZeros"),ae.cache={},ae.clearCache=()=>ae.cache={};var to=ae;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const uo=Ae,jr=to,Ur=a(t=>t!==null&&typeof t=="object"&&!Array.isArray(t),"isObject"),ro=a(t=>e=>t===!0?Number(e):String(e),"transform"),Jt=a(t=>typeof t=="number"||typeof t=="string"&&t!=="","isValidValue"),Se=a(t=>Number.isInteger(+t),"isNumber"),eu=a(t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},"zeros"),so=a((t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,"stringify$3"),no=a((t,e,u)=>{if(e>0){let r=t[0]==="-"?"-":"";r&&(t=t.slice(1)),t=r+t.padStart(r?e-1:e,"0")}return u===!1?String(t):t},"pad"),Kr=a((t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},"toMaxLen"),io=a((t,e)=>{t.negatives.sort((i,D)=>i<D?-1:i>D?1:0),t.positives.sort((i,D)=>i<D?-1:i>D?1:0);let u=e.capture?"":"?:",r="",s="",n;return t.positives.length&&(r=t.positives.join("|")),t.negatives.length&&(s=`-(${u}${t.negatives.join("|")})`),r&&s?n=`${r}|${s}`:n=r||s,e.wrap?`(${u}${n})`:n},"toSequence"),Vr=a((t,e,u,r)=>{if(u)return jr(t,e,{wrap:!1,...r});let s=String.fromCharCode(t);if(t===e)return s;let n=String.fromCharCode(e);return`[${s}-${n}]`},"toRange"),zr=a((t,e,u)=>{if(Array.isArray(t)){let r=u.wrap===!0,s=u.capture?"":"?:";return r?`(${s}${t.join("|")})`:t.join("|")}return jr(t,e,u)},"toRegex"),Yr=a((...t)=>new RangeError("Invalid range arguments: "+uo.inspect(...t)),"rangeError"),qr=a((t,e,u)=>{if(u.strictRanges===!0)throw Yr([t,e]);return[]},"invalidRange"),Do=a((t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},"invalidStep"),oo=a((t,e,u=1,r={})=>{let s=Number(t),n=Number(e);if(!Number.isInteger(s)||!Number.isInteger(n)){if(r.strictRanges===!0)throw Yr([t,e]);return[]}s===0&&(s=0),n===0&&(n=0);let i=s>n,D=String(t),o=String(e),c=String(u);u=Math.max(Math.abs(u),1);let f=eu(D)||eu(o)||eu(c),h=f?Math.max(D.length,o.length,c.length):0,l=f===!1&&so(t,e,r)===!1,p=r.transform||ro(l);if(r.toRegex&&u===1)return Vr(Kr(t,h),Kr(e,h),!0,r);let C={negatives:[],positives:[]},g=a(H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),"push"),y=[],B=0;for(;i?s>=n:s<=n;)r.toRegex===!0&&u>1?g(s):y.push(no(p(s,B),h,l)),s=i?s-u:s+u,B++;return r.toRegex===!0?u>1?io(C,r):zr(y,null,{wrap:!1,...r}):y},"fillNumbers"),ao=a((t,e,u=1,r={})=>{if(!Se(t)&&t.length>1||!Se(e)&&e.length>1)return qr(t,e,r);let s=r.transform||(l=>String.fromCharCode(l)),n=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),D=n>i,o=Math.min(n,i),c=Math.max(n,i);if(r.toRegex&&u===1)return Vr(o,c,!1,r);let f=[],h=0;for(;D?n>=i:n<=i;)f.push(s(n,h)),n=D?n-u:n+u,h++;return r.toRegex===!0?zr(f,null,{wrap:!1,options:r}):f},"fillLetters"),st=a((t,e,u,r={})=>{if(e==null&&Jt(t))return[t];if(!Jt(t)||!Jt(e))return qr(t,e,r);if(typeof u=="function")return st(t,e,1,{transform:u});if(Ur(u))return st(t,e,0,u);let s={...r};return s.capture===!0&&(s.wrap=!0),u=u||s.step||1,Se(u)?Se(t)&&Se(e)?oo(t,e,u,s):ao(t,e,Math.max(Math.abs(u),1),s):u!=null&&!Ur(u)?Do(u,s):st(t,e,1,u)},"fill$2");var Xr=st;const lo=Xr,Qr=rt,co=a((t,e={})=>{let u=a((r,s={})=>{let n=Qr.isInvalidBrace(s),i=r.invalid===!0&&e.escapeInvalid===!0,D=n===!0||i===!0,o=e.escapeInvalid===!0?"\\":"",c="";if(r.isOpen===!0||r.isClose===!0)return o+r.value;if(r.type==="open")return D?o+r.value:"(";if(r.type==="close")return D?o+r.value:")";if(r.type==="comma")return r.prev.type==="comma"?"":D?r.value:"|";if(r.value)return r.value;if(r.nodes&&r.ranges>0){let f=Qr.reduce(r.nodes),h=lo(...f,{...e,wrap:!1,toRegex:!0});if(h.length!==0)return f.length>1&&h.length>1?`(${h})`:h}if(r.nodes)for(let f of r.nodes)c+=u(f,r);return c},"walk");return u(t)},"compile$1");var fo=co;const ho=Xr,Zr=Qt,Fe=rt,le=a((t="",e="",u=!1)=>{let r=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?Fe.flatten(e).map(s=>`{${s}}`):e;for(let s of t)if(Array.isArray(s))for(let n of s)r.push(le(n,e,u));else for(let n of e)u===!0&&typeof n=="string"&&(n=`{${n}}`),r.push(Array.isArray(n)?le(s,n,u):s+n);return Fe.flatten(r)},"append"),Eo=a((t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,r=a((s,n={})=>{s.queue=[];let i=n,D=n.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,D=i.queue;if(s.invalid||s.dollar){D.push(le(D.pop(),Zr(s,e)));return}if(s.type==="brace"&&s.invalid!==!0&&s.nodes.length===2){D.push(le(D.pop(),["{}"]));return}if(s.nodes&&s.ranges>0){let h=Fe.reduce(s.nodes);if(Fe.exceedsLimit(...h,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=ho(...h,e);l.length===0&&(l=Zr(s,e)),D.push(le(D.pop(),l)),s.nodes=[];return}let o=Fe.encloseBrace(s),c=s.queue,f=s;for(;f.type!=="brace"&&f.type!=="root"&&f.parent;)f=f.parent,c=f.queue;for(let h=0;h<s.nodes.length;h++){let l=s.nodes[h];if(l.type==="comma"&&s.type==="brace"){h===1&&c.push(""),c.push("");continue}if(l.type==="close"){D.push(le(D.pop(),c,o));continue}if(l.value&&l.type!=="open"){c.push(le(c.pop(),l.value));continue}l.nodes&&r(l,s)}return c},"walk");return Fe.flatten(r(t))},"expand$1");var po=Eo,Co={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const Fo=Qt,{MAX_LENGTH:Jr,CHAR_BACKSLASH:tu,CHAR_BACKTICK:go,CHAR_COMMA:mo,CHAR_DOT:_o,CHAR_LEFT_PARENTHESES:Ao,CHAR_RIGHT_PARENTHESES:yo,CHAR_LEFT_CURLY_BRACE:wo,CHAR_RIGHT_CURLY_BRACE:Ro,CHAR_LEFT_SQUARE_BRACKET:es,CHAR_RIGHT_SQUARE_BRACKET:ts,CHAR_DOUBLE_QUOTE:bo,CHAR_SINGLE_QUOTE:vo,CHAR_NO_BREAK_SPACE:So,CHAR_ZERO_WIDTH_NOBREAK_SPACE:Bo}=Co,$o=a((t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},r=typeof u.maxLength=="number"?Math.min(Jr,u.maxLength):Jr;if(t.length>r)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${r})`);let s={type:"root",input:t,nodes:[]},n=[s],i=s,D=s,o=0,c=t.length,f=0,h=0,l;const p=a(()=>t[f++],"advance"),C=a(g=>{if(g.type==="text"&&D.type==="dot"&&(D.type="text"),D&&D.type==="text"&&g.type==="text"){D.value+=g.value;return}return i.nodes.push(g),g.parent=i,g.prev=D,D=g,g},"push");for(C({type:"bos"});f<c;)if(i=n[n.length-1],l=p(),!(l===Bo||l===So)){if(l===tu){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===ts){C({type:"text",value:"\\"+l});continue}if(l===es){o++;let g;for(;f<c&&(g=p());){if(l+=g,g===es){o++;continue}if(g===tu){l+=p();continue}if(g===ts&&(o--,o===0))break}C({type:"text",value:l});continue}if(l===Ao){i=C({type:"paren",nodes:[]}),n.push(i),C({type:"text",value:l});continue}if(l===yo){if(i.type!=="paren"){C({type:"text",value:l});continue}i=n.pop(),C({type:"text",value:l}),i=n[n.length-1];continue}if(l===bo||l===vo||l===go){let g=l,y;for(e.keepQuotes!==!0&&(l="");f<c&&(y=p());){if(y===tu){l+=y+p();continue}if(y===g){e.keepQuotes===!0&&(l+=y);break}l+=y}C({type:"text",value:l});continue}if(l===wo){h++;let y={type:"brace",open:!0,close:!1,dollar:D.value&&D.value.slice(-1)==="$"||i.dollar===!0,depth:h,commas:0,ranges:0,nodes:[]};i=C(y),n.push(i),C({type:"open",value:l});continue}if(l===Ro){if(i.type!=="brace"){C({type:"text",value:l});continue}let g="close";i=n.pop(),i.close=!0,C({type:g,value:l}),h--,i=n[n.length-1];continue}if(l===mo&&h>0){if(i.ranges>0){i.ranges=0;let g=i.nodes.shift();i.nodes=[g,{type:"text",value:Fo(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===_o&&h>0&&i.commas===0){let g=i.nodes;if(h===0||g.length===0){C({type:"text",value:l});continue}if(D.type==="dot"){if(i.range=[],D.value+=l,D.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,D.type="text";continue}i.ranges++,i.args=[];continue}if(D.type==="range"){g.pop();let y=g[g.length-1];y.value+=D.value+l,D=y,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=n.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let g=n[n.length-1],y=g.nodes.indexOf(i);g.nodes.splice(y,1,...i.nodes)}while(n.length>0);return C({type:"eos"}),s},"parse$1");var To=$o;const us=Qt,xo=fo,Oo=po,No=To,q=a((t,e={})=>{let u=[];if(Array.isArray(t))for(let r of t){let s=q.create(r,e);Array.isArray(s)?u.push(...s):u.push(s)}else u=[].concat(q.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u},"braces$1");q.parse=(t,e={})=>No(t,e),q.stringify=(t,e={})=>us(typeof t=="string"?q.parse(t,e):t,e),q.compile=(t,e={})=>(typeof t=="string"&&(t=q.parse(t,e)),xo(t,e)),q.expand=(t,e={})=>{typeof t=="string"&&(t=q.parse(t,e));let u=Oo(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},q.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?q.compile(t,e):q.expand(t,e);var Ho=q,Lo=["3dm","3ds","3g2","3gp","7z","a","aac","adp","afdesign","afphoto","afpub","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"],Io=Lo;const Po=z,ko=Io,Mo=new Set(ko);var Go=a(t=>Mo.has(Po.extname(t).slice(1).toLowerCase()),"isBinaryPath$1"),nt={};(function(t){const{sep:e}=z,{platform:u}=process,r=bu;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_FLAG_MUST_SCAN_SUBDIRS=1,t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=s=>s,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=r.type()==="OS400"})(nt);const re=De,P=z,{promisify:Be}=Ae,Wo=Go,{isWindows:jo,isLinux:Uo,EMPTY_FN:Ko,EMPTY_STR:Vo,KEY_LISTENERS:ge,KEY_ERR:uu,KEY_RAW:$e,HANDLER_KEYS:zo,EV_CHANGE:it,EV_ADD:Dt,EV_ADD_DIR:Yo,EV_ERROR:rs,STR_DATA:qo,STR_END:Xo,BRACE_START:Qo,STAR:Zo}=nt,Jo="watch",ea=Be(re.open),ss=Be(re.stat),ta=Be(re.lstat),ua=Be(re.close),ru=Be(re.realpath),ra={lstat:ta,stat:ss},su=a((t,e)=>{t instanceof Set?t.forEach(e):e(t)},"foreach"),Te=a((t,e,u)=>{let r=t[e];r instanceof Set||(t[e]=r=new Set([r])),r.add(u)},"addAndConvert"),sa=a(t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},"clearItem"),xe=a((t,e,u)=>{const r=t[e];r instanceof Set?r.delete(u):r===u&&delete t[e]},"delFromSet"),ns=a(t=>t instanceof Set?t.size===0:!t,"isEmptySet"),ot=new Map;function is(t,e,u,r,s){const n=a((i,D)=>{u(t),s(i,D,{watchedPath:t}),D&&t!==D&&at(P.resolve(t,D),ge,P.join(t,D))},"handleEvent");try{return re.watch(t,e,n)}catch(i){r(i)}}a(is,"createFsWatchInstance");const at=a((t,e,u,r,s)=>{const n=ot.get(t);n&&su(n[e],i=>{i(u,r,s)})},"fsWatchBroadcast"),na=a((t,e,u,r)=>{const{listener:s,errHandler:n,rawEmitter:i}=r;let D=ot.get(e),o;if(!u.persistent)return o=is(t,u,s,n,i),o.close.bind(o);if(D)Te(D,ge,s),Te(D,uu,n),Te(D,$e,i);else{if(o=is(t,u,at.bind(null,e,ge),n,at.bind(null,e,$e)),!o)return;o.on(rs,async c=>{const f=at.bind(null,e,uu);if(D.watcherUnusable=!0,jo&&c.code==="EPERM")try{const h=await ea(t,"r");await ua(h),f(c)}catch{}else f(c)}),D={listeners:s,errHandlers:n,rawEmitters:i,watcher:o},ot.set(e,D)}return()=>{xe(D,ge,s),xe(D,uu,n),xe(D,$e,i),ns(D.listeners)&&(D.watcher.close(),ot.delete(e),zo.forEach(sa(D)),D.watcher=void 0,Object.freeze(D))}},"setFsWatchListener"),nu=new Map,ia=a((t,e,u,r)=>{const{listener:s,rawEmitter:n}=r;let i=nu.get(e);const D=i&&i.options;return D&&(D.persistent<u.persistent||D.interval>u.interval)&&(i.listeners,i.rawEmitters,re.unwatchFile(e),i=void 0),i?(Te(i,ge,s),Te(i,$e,n)):(i={listeners:s,rawEmitters:n,options:u,watcher:re.watchFile(e,u,(o,c)=>{su(i.rawEmitters,h=>{h(it,e,{curr:o,prev:c})});const f=o.mtimeMs;(o.size!==c.size||f>c.mtimeMs||f===0)&&su(i.listeners,h=>h(t,o))})},nu.set(e,i)),()=>{xe(i,ge,s),xe(i,$e,n),ns(i.listeners)&&(nu.delete(e),re.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}},"setFsWatchFileListener");let Da=class{static{a(this,"NodeFsHandler")}constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const r=this.fsw.options,s=P.dirname(e),n=P.basename(e);this.fsw._getWatchedDir(s).add(n);const D=P.resolve(e),o={persistent:r.persistent};u||(u=Ko);let c;return r.usePolling?(o.interval=r.enableBinaryInterval&&Wo(n)?r.binaryInterval:r.interval,c=ia(e,D,o,{listener:u,rawEmitter:this.fsw._emitRaw})):c=na(e,D,o,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),c}_handleFile(e,u,r){if(this.fsw.closed)return;const s=P.dirname(e),n=P.basename(e),i=this.fsw._getWatchedDir(s);let D=u;if(i.has(n))return;const o=a(async(f,h)=>{if(this.fsw._throttle(Jo,e,5)){if(!h||h.mtimeMs===0)try{const l=await ss(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==D.mtimeMs)&&this.fsw._emit(it,e,l),Uo&&D.ino!==l.ino?(this.fsw._closeFile(f),D=l,this.fsw._addPathCloser(f,this._watchWithNodeFs(e,o))):D=l}catch{this.fsw._remove(s,n)}else if(i.has(n)){const l=h.atimeMs,p=h.mtimeMs;(!l||l<=p||p!==D.mtimeMs)&&this.fsw._emit(it,e,h),D=h}}},"listener"),c=this._watchWithNodeFs(e,o);if(!(r&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(Dt,e,0))return;this.fsw._emit(Dt,e,u)}return c}async _handleSymlink(e,u,r,s){if(this.fsw.closed)return;const n=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let D;try{D=await ru(r)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(s)?this.fsw._symlinkPaths.get(n)!==D&&(this.fsw._symlinkPaths.set(n,D),this.fsw._emit(it,r,e.stats)):(i.add(s),this.fsw._symlinkPaths.set(n,D),this.fsw._emit(Dt,r,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(n))return!0;this.fsw._symlinkPaths.set(n,!0)}_handleRead(e,u,r,s,n,i,D){if(e=P.join(e,Vo),!r.hasGlob&&(D=this.fsw._throttle("readdir",e,1e3),!D))return;const o=this.fsw._getWatchedDir(r.path),c=new Set;let f=this.fsw._readdirp(e,{fileFilter:a(h=>r.filterPath(h),"fileFilter"),directoryFilter:a(h=>r.filterDir(h),"directoryFilter"),depth:0}).on(qo,async h=>{if(this.fsw.closed){f=void 0;return}const l=h.path;let p=P.join(e,l);if(c.add(l),!(h.stats.isSymbolicLink()&&await this._handleSymlink(h,e,p,l))){if(this.fsw.closed){f=void 0;return}(l===s||!s&&!o.has(l))&&(this.fsw._incrReadyCount(),p=P.join(n,P.relative(n,p)),this._addToNodeFs(p,u,r,i+1))}}).on(rs,this._boundHandleError);return new Promise(h=>f.once(Xo,()=>{if(this.fsw.closed){f=void 0;return}const l=D?D.clear():!1;h(),o.getChildren().filter(p=>p!==e&&!c.has(p)&&(!r.hasGlob||r.filterPath({fullPath:P.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),f=void 0,l&&this._handleRead(e,!1,r,s,n,i,D)}))}async _handleDir(e,u,r,s,n,i,D){const o=this.fsw._getWatchedDir(P.dirname(e)),c=o.has(P.basename(e));!(r&&this.fsw.options.ignoreInitial)&&!n&&!c&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(Yo,e,u),o.add(P.basename(e)),this.fsw._getWatchedDir(e);let f,h;const l=this.fsw.options.depth;if((l==null||s<=l)&&!this.fsw._symlinkPaths.has(D)){if(!n&&(await this._handleRead(e,r,i,n,e,s,f),this.fsw.closed))return;h=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,n,e,s,f)})}return h}async _addToNodeFs(e,u,r,s,n){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const D=this.fsw._getWatchHelpers(e,s);!D.hasGlob&&r&&(D.hasGlob=r.hasGlob,D.globFilter=r.globFilter,D.filterPath=o=>r.filterPath(o),D.filterDir=o=>r.filterDir(o));try{const o=await ra[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))return i(),!1;const c=this.fsw.options.followSymlinks&&!e.includes(Zo)&&!e.includes(Qo);let f;if(o.isDirectory()){const h=P.resolve(e),l=c?await ru(e):e;if(this.fsw.closed||(f=await this._handleDir(D.watchPath,o,u,s,n,D,l),this.fsw.closed))return;h!==l&&l!==void 0&&this.fsw._symlinkPaths.set(h,l)}else if(o.isSymbolicLink()){const h=c?await ru(e):e;if(this.fsw.closed)return;const l=P.dirname(D.watchPath);if(this.fsw._getWatchedDir(l).add(D.watchPath),this.fsw._emit(Dt,D.watchPath,o),f=await this._handleDir(l,o,u,s,e,D,h),this.fsw.closed)return;h!==void 0&&this.fsw._symlinkPaths.set(P.resolve(e),h)}else f=this._handleFile(D.watchPath,o,u);return i(),this.fsw._addPathCloser(e,f),!1}catch(o){if(this.fsw._handleError(o))return i(),e}}};var oa=Da,iu={exports:{}};const Du=De,k=z,{promisify:ou}=Ae;let me;try{me=he.require("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(me){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(me=void 0)}}const{EV_ADD:au,EV_CHANGE:aa,EV_ADD_DIR:Ds,EV_UNLINK:lt,EV_ERROR:la,STR_DATA:ca,STR_END:fa,FSEVENT_CREATED:ha,FSEVENT_MODIFIED:da,FSEVENT_DELETED:Ea,FSEVENT_MOVED:pa,FSEVENT_UNKNOWN:Ca,FSEVENT_FLAG_MUST_SCAN_SUBDIRS:Fa,FSEVENT_TYPE_FILE:ga,FSEVENT_TYPE_DIRECTORY:Oe,FSEVENT_TYPE_SYMLINK:os,ROOT_GLOBSTAR:as,DIR_SUFFIX:ma,DOT_SLASH:ls,FUNCTION_TYPE:lu,EMPTY_FN:_a,IDENTITY_FN:Aa}=nt,ya=a(t=>isNaN(t)?{}:{depth:t},"Depth"),cu=ou(Du.stat),wa=ou(Du.lstat),cs=ou(Du.realpath),Ra={stat:cu,lstat:wa},ce=new Map,ba=10,va=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),Sa=a((t,e)=>({stop:me.watch(t,e)}),"createFSEventsInstance");function Ba(t,e,u,r){let s=k.extname(e)?k.dirname(e):e;const n=k.dirname(s);let i=ce.get(s);$a(n)&&(s=n);const D=k.resolve(t),o=D!==e,c=a((h,l,p)=>{o&&(h=h.replace(e,D)),(h===D||!h.indexOf(D+k.sep))&&u(h,l,p)},"filteredListener");let f=!1;for(const h of ce.keys())if(e.indexOf(k.resolve(h)+k.sep)===0){s=h,i=ce.get(s),f=!0;break}return i||f?i.listeners.add(c):(i={listeners:new Set([c]),rawEmitter:r,watcher:Sa(s,(h,l)=>{if(!i.listeners.size||l&Fa)return;const p=me.getInfo(h,l);i.listeners.forEach(C=>{C(h,l,p)}),i.rawEmitter(p.event,h,p)})},ce.set(s,i)),()=>{const h=i.listeners;if(h.delete(c),!h.size&&(ce.delete(s),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}a(Ba,"setFSEventsListener");const $a=a(t=>{let e=0;for(const u of ce.keys())if(u.indexOf(t)===0&&(e++,e>=ba))return!0;return!1},"couldConsolidate"),Ta=a(()=>me&&ce.size<128,"canUse"),fu=a((t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=k.dirname(t))!==e;)u++;return u},"calcDepth"),fs=a((t,e)=>t.type===Oe&&e.isDirectory()||t.type===os&&e.isSymbolicLink()||t.type===ga&&e.isFile(),"sameTypes");let xa=class{static{a(this,"FsEventsHandler")}constructor(e){this.fsw=e}checkIgnored(e,u){const r=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return r.add(e),u&&u.isDirectory()&&r.add(e+as),!0;r.delete(e),r.delete(e+as)}addOrChange(e,u,r,s,n,i,D,o){const c=n.has(i)?aa:au;this.handleEvent(c,e,u,r,s,n,i,D,o)}async checkExists(e,u,r,s,n,i,D,o){try{const c=await cu(e);if(this.fsw.closed)return;fs(D,c)?this.addOrChange(e,u,r,s,n,i,D,o):this.handleEvent(lt,e,u,r,s,n,i,D,o)}catch(c){c.code==="EACCES"?this.addOrChange(e,u,r,s,n,i,D,o):this.handleEvent(lt,e,u,r,s,n,i,D,o)}}handleEvent(e,u,r,s,n,i,D,o,c){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===lt){const f=o.type===Oe;(f||i.has(D))&&this.fsw._remove(n,D,f)}else{if(e===au){if(o.type===Oe&&this.fsw._getWatchedDir(u),o.type===os&&c.followSymlinks){const h=c.depth===void 0?void 0:fu(r,s)+1;return this._addToFsEvents(u,!1,!0,h)}this.fsw._getWatchedDir(n).add(D)}const f=o.type===Oe?e+ma:e;this.fsw._emit(f,u),f===Ds&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,r,s){if(this.fsw.closed||this.fsw._isIgnored(e))return;const n=this.fsw.options,D=Ba(e,u,a(async(o,c,f)=>{if(this.fsw.closed||n.depth!==void 0&&fu(o,u)>n.depth)return;const h=r(k.join(e,k.relative(e,o)));if(s&&!s(h))return;const l=k.dirname(h),p=k.basename(h),C=this.fsw._getWatchedDir(f.type===Oe?h:l);if(va.has(c)||f.event===Ca)if(typeof n.ignored===lu){let g;try{g=await cu(h)}catch{}if(this.fsw.closed||this.checkIgnored(h,g))return;fs(f,g)?this.addOrChange(h,o,u,l,C,p,f,n):this.handleEvent(lt,h,o,u,l,C,p,f,n)}else this.checkExists(h,o,u,l,C,p,f,n);else switch(f.event){case ha:case da:return this.addOrChange(h,o,u,l,C,p,f,n);case Ea:case pa:return this.checkExists(h,o,u,l,C,p,f,n)}},"watchCallback"),this.fsw._emitRaw);return this.fsw._emitReady(),D}async _handleFsEventsSymlink(e,u,r,s){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const n=await cs(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(n))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(n||e,i=>{let D=e;return n&&n!==ls?D=i.replace(n,e):i!==ls&&(D=k.join(e,i)),r(D)},!1,s)}catch(n){if(this.fsw._handleError(n))return this.fsw._emitReady()}}}emitAdd(e,u,r,s,n){const i=r(e),D=u.isDirectory(),o=this.fsw._getWatchedDir(k.dirname(i)),c=k.basename(i);D&&this.fsw._getWatchedDir(i),!o.has(c)&&(o.add(c),(!s.ignoreInitial||n===!0)&&this.fsw._emit(D?Ds:au,i,u))}initWatch(e,u,r,s){if(this.fsw.closed)return;const n=this._watchWithFsEvents(r.watchPath,k.resolve(e||r.watchPath),s,r.globFilter);this.fsw._addPathCloser(u,n)}async _addToFsEvents(e,u,r,s){if(this.fsw.closed)return;const n=this.fsw.options,i=typeof u===lu?u:Aa,D=this.fsw._getWatchHelpers(e);try{const o=await Ra[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))throw null;if(o.isDirectory()){if(D.globFilter||this.emitAdd(i(e),o,i,n,r),s&&s>n.depth)return;this.fsw._readdirp(D.watchPath,{fileFilter:a(c=>D.filterPath(c),"fileFilter"),directoryFilter:a(c=>D.filterDir(c),"directoryFilter"),...ya(n.depth-(s||0))}).on(ca,c=>{if(this.fsw.closed||c.stats.isDirectory()&&!D.filterPath(c))return;const f=k.join(D.watchPath,c.path),{fullPath:h}=c;if(D.followSymlinks&&c.stats.isSymbolicLink()){const l=n.depth===void 0?void 0:fu(f,k.resolve(D.watchPath))+1;this._handleFsEventsSymlink(f,h,i,l)}else this.emitAdd(f,c.stats,i,n,r)}).on(la,_a).on(fa,()=>{this.fsw._emitReady()})}else this.emitAdd(D.watchPath,o,i,n,r),this.fsw._emitReady()}catch(o){(!o||this.fsw._handleError(o))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(n.persistent&&r!==!0)if(typeof u===lu)this.initWatch(void 0,e,D,i);else{let o;try{o=await cs(D.watchPath)}catch{}this.initWatch(o,e,D,i)}}};iu.exports=xa,iu.exports.canUse=Ta;var Oa=iu.exports;const{EventEmitter:Na}=Ls,hu=De,S=z,{promisify:hs}=Ae,Ha=SD,du=ND.default,La=VD,Eu=Nr,Ia=Ho,Pa=Br,ka=oa,ds=Oa,{EV_ALL:pu,EV_READY:Ma,EV_ADD:ct,EV_CHANGE:Ne,EV_UNLINK:Es,EV_ADD_DIR:Ga,EV_UNLINK_DIR:Wa,EV_RAW:ja,EV_ERROR:Cu,STR_CLOSE:Ua,STR_END:Ka,BACK_SLASH_RE:Va,DOUBLE_SLASH_RE:ps,SLASH_OR_BACK_SLASH_RE:za,DOT_RE:Ya,REPLACER_RE:qa,SLASH:Fu,SLASH_SLASH:Xa,BRACE_START:Qa,BANG:gu,ONE_DOT:Cs,TWO_DOTS:Za,GLOBSTAR:Ja,SLASH_GLOBSTAR:mu,ANYMATCH_OPTS:_u,STRING_TYPE:Au,FUNCTION_TYPE:el,EMPTY_STR:yu,EMPTY_FN:tl,isWindows:ul,isMacos:rl,isIBMi:sl}=nt,nl=hs(hu.stat),il=hs(hu.readdir),wu=a((t=[])=>Array.isArray(t)?t:[t],"arrify"),Fs=a((t,e=[])=>(t.forEach(u=>{Array.isArray(u)?Fs(u,e):e.push(u)}),e),"flatten"),gs=a(t=>{const e=Fs(wu(t));if(!e.every(u=>typeof u===Au))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(_s)},"unifyPaths"),ms=a(t=>{let e=t.replace(Va,Fu),u=!1;for(e.startsWith(Xa)&&(u=!0);e.match(ps);)e=e.replace(ps,Fu);return u&&(e=Fu+e),e},"toUnix"),_s=a(t=>ms(S.normalize(ms(t))),"normalizePathToUnix"),As=a((t=yu)=>e=>typeof e!==Au?e:_s(S.isAbsolute(e)?e:S.join(t,e)),"normalizeIgnored"),Dl=a((t,e)=>S.isAbsolute(t)?t:t.startsWith(gu)?gu+S.join(e,t.slice(1)):S.join(e,t),"getAbsolutePath"),X=a((t,e)=>t[e]===void 0,"undef");class ol{static{a(this,"DirEntry")}constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;u&&e!==Cs&&e!==Za&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const r=this.path;try{await il(r)}catch{this._removeWatcher&&this._removeWatcher(S.dirname(r),S.basename(r))}}has(e){const{items:u}=this;if(u)return u.has(e)}getChildren(){const{items:e}=this;if(e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const al="stat",ll="lstat";class cl{static{a(this,"WatchHelper")}constructor(e,u,r,s){this.fsw=s,this.path=e=e.replace(qa,yu),this.watchPath=u,this.fullWatchPath=S.resolve(u),this.hasGlob=u!==e,e===yu&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&r?void 0:!1,this.globFilter=this.hasGlob?du(e,void 0,_u):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(n=>{n.length>1&&n.pop()}),this.followSymlinks=r,this.statMethod=r?al:ll}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return S.join(this.watchPath,S.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const r=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===el?this.globFilter(r):!0)&&this.fsw._isntIgnored(r,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(Qa)?Ia.expand(e):[e]).forEach(s=>{u.push(S.relative(this.watchPath,s).split(za))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let r=!1;this.unmatchedGlob=!this.dirParts.some(s=>s.every((n,i)=>(n===Ja&&(r=!0),r||!u[0][i]||du(n,u[0][i],_u))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class fl extends Na{static{a(this,"FSWatcher")}constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,X(u,"persistent")&&(u.persistent=!0),X(u,"ignoreInitial")&&(u.ignoreInitial=!1),X(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),X(u,"interval")&&(u.interval=100),X(u,"binaryInterval")&&(u.binaryInterval=300),X(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,X(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),ds.canUse()||(u.useFsEvents=!1),X(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=rl),sl&&(u.usePolling=!0);const s=process.env.CHOKIDAR_USEPOLLING;if(s!==void 0){const o=s.toLowerCase();o==="false"||o==="0"?u.usePolling=!1:o==="true"||o==="1"?u.usePolling=!0:u.usePolling=!!o}const n=process.env.CHOKIDAR_INTERVAL;n&&(u.interval=Number.parseInt(n,10)),X(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),X(u,"followSymlinks")&&(u.followSymlinks=!0),X(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=wu(u.ignored));let D=0;this._emitReady=()=>{D++,D>=this._readyCount&&(this._emitReady=tl,this._readyEmitted=!0,process.nextTick(()=>this.emit(Ma)))},this._emitRaw=(...o)=>this.emit(ja,...o),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new ds(this):this._nodeFsHandler=new ka(this),Object.freeze(u)}add(e,u,r){const{cwd:s,disableGlobbing:n}=this.options;this.closed=!1;let i=gs(e);return s&&(i=i.map(D=>{const o=Dl(D,s);return n||!Eu(D)?o:Pa(o)})),i=i.filter(D=>D.startsWith(gu)?(this._ignoredPaths.add(D.slice(1)),!1):(this._ignoredPaths.delete(D),this._ignoredPaths.delete(D+mu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount+=i.length),i.forEach(D=>this._fsEventsHandler._addToFsEvents(D))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async D=>{const o=await this._nodeFsHandler._addToNodeFs(D,!r,0,0,u);return o&&this._emitReady(),o})).then(D=>{this.closed||D.filter(o=>o).forEach(o=>{this.add(S.dirname(o),S.basename(u||o))})})),this}unwatch(e){if(this.closed)return this;const u=gs(e),{cwd:r}=this.options;return u.forEach(s=>{!S.isAbsolute(s)&&!this._closers.has(s)&&(r&&(s=S.join(r,s)),s=S.resolve(s)),this._closePath(s),this._ignoredPaths.add(s),this._watched.has(s)&&this._ignoredPaths.add(s+mu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(r=>{const s=r();s instanceof Promise&&e.push(s)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,r)=>{const s=this.options.cwd?S.relative(this.options.cwd,r):r;e[s||Cs]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==Cu&&this.emit(pu,...u)}async _emit(e,u,r,s,n){if(this.closed)return;const i=this.options;ul&&(u=S.normalize(u)),i.cwd&&(u=S.relative(i.cwd,u));const D=[e,u];n!==void 0?D.push(r,s,n):s!==void 0?D.push(r,s):r!==void 0&&D.push(r);const o=i.awaitWriteFinish;let c;if(o&&(c=this._pendingWrites.get(u)))return c.lastChange=new Date,this;if(i.atomic){if(e===Es)return this._pendingUnlinks.set(u,D),setTimeout(()=>{this._pendingUnlinks.forEach((f,h)=>{this.emit(...f),this.emit(pu,...f),this._pendingUnlinks.delete(h)})},typeof i.atomic=="number"?i.atomic:100),this;e===ct&&this._pendingUnlinks.has(u)&&(e=D[0]=Ne,this._pendingUnlinks.delete(u))}if(o&&(e===ct||e===Ne)&&this._readyEmitted){const f=a((h,l)=>{h?(e=D[0]=Cu,D[1]=h,this.emitWithAll(e,D)):l&&(D.length>2?D[2]=l:D.push(l),this.emitWithAll(e,D))},"awfEmit");return this._awaitWriteFinish(u,o.stabilityThreshold,e,f),this}if(e===Ne&&!this._throttle(Ne,u,50))return this;if(i.alwaysStat&&r===void 0&&(e===ct||e===Ga||e===Ne)){const f=i.cwd?S.join(i.cwd,u):u;let h;try{h=await nl(f)}catch{}if(!h||this.closed)return;D.push(h)}return this.emitWithAll(e,D),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(Cu,e),e||this.closed}_throttle(e,u,r){this._throttled.has(e)||this._throttled.set(e,new Map);const s=this._throttled.get(e),n=s.get(u);if(n)return n.count++,!1;let i;const D=a(()=>{const c=s.get(u),f=c?c.count:0;return s.delete(u),clearTimeout(i),c&&clearTimeout(c.timeoutObject),f},"clear");i=setTimeout(D,r);const o={timeoutObject:i,clear:D,count:0};return s.set(u,o),o}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,r,s){let n,i=e;this.options.cwd&&!S.isAbsolute(e)&&(i=S.join(this.options.cwd,e));const D=new Date,o=a(c=>{hu.stat(i,(f,h)=>{if(f||!this._pendingWrites.has(e)){f&&f.code!=="ENOENT"&&s(f);return}const l=Number(new Date);c&&h.size!==c.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),s(void 0,h)):n=setTimeout(o,this.options.awaitWriteFinish.pollInterval,h)})},"awaitWriteFinish");this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:D,cancelWait:a(()=>(this._pendingWrites.delete(e),clearTimeout(n),r),"cancelWait")}),n=setTimeout(o,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&Ya.test(e))return!0;if(!this._userIgnored){const{cwd:r}=this.options,s=this.options.ignored,n=s&&s.map(As(r)),i=wu(n).filter(o=>typeof o===Au&&!Eu(o)).map(o=>o+mu),D=this._getGlobIgnored().map(As(r)).concat(n,i);this._userIgnored=du(D,void 0,_u)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const r=u||this.options.disableGlobbing||!Eu(e)?e:La(e),s=this.options.followSymlinks;return new cl(e,r,s,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=S.resolve(e);return this._watched.has(u)||this._watched.set(u,new ol(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const r=(e&&Number.parseInt(e.mode,10))&511;return!!(4&Number.parseInt(r.toString(8)[0],10))}_remove(e,u,r){const s=S.join(e,u),n=S.resolve(s);if(r=r??(this._watched.has(s)||this._watched.has(n)),!this._throttle("remove",s,100))return;!r&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(s).getChildren().forEach(l=>this._remove(s,l));const o=this._getWatchedDir(e),c=o.has(u);o.remove(u),this._symlinkPaths.has(n)&&this._symlinkPaths.delete(n);let f=s;if(this.options.cwd&&(f=S.relative(this.options.cwd,s)),this.options.awaitWriteFinish&&this._pendingWrites.has(f)&&this._pendingWrites.get(f).cancelWait()===ct)return;this._watched.delete(s),this._watched.delete(n);const h=r?Wa:Es;c&&!this._isIgnored(s)&&this._emit(h,s),this.options.useFsEvents||this._closePath(s)}_closePath(e){this._closeFile(e);const u=S.dirname(e);this._getWatchedDir(u).remove(S.basename(e))}_closeFile(e){const u=this._closers.get(e);u&&(u.forEach(r=>r()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let r=this._closers.get(e);r||(r=[],this._closers.set(e,r)),r.push(u)}_readdirp(e,u){if(this.closed)return;const r={type:pu,alwaysStat:!0,lstat:!0,...u};let s=Ha(e,r);return this._streams.add(s),s.once(Ua,()=>{s=void 0}),s.once(Ka,()=>{s&&(this._streams.delete(s),s=void 0)}),s}}const hl=a((t,e)=>{const u=new fl(e);return u.add(t),u},"watch");var dl=hl;let fe=!0;const _e=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let He=0;if(_e.process&&_e.process.env&&_e.process.stdout){const{FORCE_COLOR:t,NODE_DISABLE_COLORS:e,NO_COLOR:u,TERM:r,COLORTERM:s}=_e.process.env;e||u||t==="0"?fe=!1:t==="1"||t==="2"||t==="3"?fe=!0:r==="dumb"?fe=!1:"CI"in _e.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(n=>n in _e.process.env)?fe=!0:fe=process.stdout.isTTY,fe&&(process.platform==="win32"||s&&(s==="truecolor"||s==="24bit")?He=3:r&&(r.endsWith("-256color")||r.endsWith("256"))?He=2:He=1)}let ys={enabled:fe,supportLevel:He};function Le(t,e,u=1){const r=`\x1B[${t}m`,s=`\x1B[${e}m`,n=new RegExp(`\\x1b\\[${e}m`,"g");return i=>ys.enabled&&ys.supportLevel>=u?r+(""+i).replace(n,r)+s:""+i}a(Le,"kolorist");const Ie=Le(33,39),El=Le(90,39),pl=Le(92,39),Cl=Le(95,39),Fl=Le(96,39),ft=a((t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}},"ignoreAfterArgument"),ws=a((t,e=process.argv.slice(2))=>(Su(t,e,{ignore:ft()}),e),"removeArgvFlags"),gl=a(t=>{let e=Buffer.alloc(0);return u=>{for(e=Buffer.concat([e,u]);e.length>4;){const r=e.readInt32BE(0);if(e.length>=4+r){const s=e.slice(4,4+r);t(s),e=e.slice(4+r)}else break}}},"bufferData"),Rs=a(async()=>{const t=Ps.createServer(u=>{u.on("data",gl(r=>{const s=JSON.parse(r.toString());t.emit("data",s)}))}),e=he.getPipePath(process.pid);return await pt.promises.mkdir(ks.tmpdir,{recursive:!0}),await pt.promises.rm(e,{force:!0}),await new Promise((u,r)=>{t.listen(e,u),t.on("error",r)}),t.unref(),process.on("exit",()=>{if(t.close(),!he.isWindows)try{pt.rmSync(e)}catch{}}),t},"createIpcServer"),ml=a(()=>new Date().toLocaleTimeString(),"currentTime"),Pe=a((...t)=>console.log(El(ml()),Fl("[tsx]"),...t),"log"),_l="\x1Bc",Al=a((t,e)=>{let u;return function(){u&&clearTimeout(u),u=setTimeout(()=>Reflect.apply(t,this,arguments),e)}},"debounce"),bs={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched (Deprecated: use --exclude)"},include:{type:[String],description:"Additional paths & globs to watch"},exclude:{type:[String],description:"Paths & globs to exclude from being watched"}},yl=si({name:"watch",parameters:["<script path>"],flags:bs,help:{description:"Run the script and watch for changes"},ignoreArgv:ft(!1)},async t=>{const e=ws(bs,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,include:t.flags.include,exclude:[...t.flags.ignore,...t.flags.exclude],ipc:!0};let r,s=!1;(await Rs()).on("data",l=>{if(l&&typeof l=="object"&&"type"in l&&l.type==="dependency"&&"path"in l&&typeof l.path=="string"){const p=l.path.startsWith("file:")?Ru.fileURLToPath(l.path):l.path;Hs.isAbsolute(p)&&h.add(p)}});const i=a(()=>{if(!s)return lr(e,u)},"spawnProcess");let D=!1;const o=a(async(l,p="SIGTERM",C=5e3)=>{let g=!1;const y=new Promise(B=>{l.on("exit",H=>{g=!0,D=!1,B(H)})});return D=!0,l.kill(p),setTimeout(()=>{g||(Pe(Ie(`Process didn't exit in ${Math.floor(C/1e3)}s. Force killing...`)),l.kill("SIGKILL"))},C),await y},"killProcess"),c=Al(async(l,p)=>{const C=l?`${l?Cl(l):""}${p?` in ${pl(`./${p}`)}`:""}`:"";if(D){Pe(C,Ie("Process hasn't exited. Killing process...")),r.kill("SIGKILL");return}r&&(r.exitCode===null?(Pe(C,Ie("Restarting...")),await o(r)):Pe(C,Ie("Rerunning...")),u.clearScreen&&process.stdout.write(_l)),r=i()},100);c();const f=a(l=>{s=!0,r?.exitCode===null?(D&&Pe(Ie("Previous process hasn't exited yet. Force killing...")),o(r,D?"SIGKILL":l).then(p=>{process.exit(p??0)},()=>{})):process.exit(Et.constants.signals[l])},"relaySignal");process.on("SIGINT",f),process.on("SIGTERM",f);const h=dl([...t._,...u.include],{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.exclude],ignorePermissionErrors:!0}).on("all",c);process.stdin.on("data",()=>c("Return key"))}),wl=a((t,e)=>{let u;e.on("data",n=>{n&&n.type==="signal"&&u&&u(n.signal)});const r=a(()=>{const n=new Promise(i=>{setTimeout(()=>i(void 0),30),u=i});return n.then(()=>{u=void 0},()=>{}),n},"waitForSignalFromChild"),s=a(async n=>{await r()!==n&&(t.kill(n),await r()!==n&&(t.on("exit",()=>{const o=Et.constants.signals[n];process.exit(128+o)}),t.kill("SIGKILL")))},"relaySignalToChild");process.on("SIGINT",s),process.on("SIGTERM",s)},"relaySignals"),vs={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};Yu({name:"tsx",parameters:["[script path]"],commands:[yl],flags:{...vs,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:ft()},async t=>{t.flags.version?process.stdout.write(`tsx v${Os.version}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e={eval:{type:String,alias:"e"},print:{type:String,alias:"p"}},{_:u,flags:r}=Yu({flags:{...e,inputType:String,test:Boolean},help:!1,ignoreArgv:ft(!1)}),s=ws({...vs,...e}),i=["print","eval"].find(c=>!!r[c]);if(i){const{inputType:c}=r,f=r[i],h=xs.transformSync(f,{loader:"default",sourcefile:"/eval.ts",format:c==="module"?"esm":"cjs"});s.unshift(`--${i}`,h.code)}je.isFeatureSupported(je.testRunnerGlob)&&r.test&&u.length===0&&s.push("**/{test,test/**/*,test-*,*[.-_]test}.?(c|m)@(t|j)s");const D=await Rs(),o=lr(s,{noCache:!!t.flags.noCache,tsconfigPath:t.flags.tsconfig});wl(o,D),process.send&&o.on("message",c=>{process.send(c)}),o.send&&process.on("message",c=>{o.send(c)}),o.on("close",c=>{c===null&&(c=Et.constants.signals[o.signalCode]+128),process.exit(c)})});
