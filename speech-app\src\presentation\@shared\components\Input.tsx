import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  id: string;
  textarea?: boolean;
  rows?: number;
  disabled?: boolean;
  loading?: boolean; // Added loading prop
}

const Input: React.FC<InputProps> = ({ label, id, textarea, rows, className, disabled, loading, ...props }) => {
  const baseClasses = "w-full px-4 py-2 border rounded transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-400/30 focus:border-transparent"; // Adjusted rounded, focus ring, and border on focus
  const disabledClasses = "bg-gray-100 cursor-not-allowed";
  const loadingClasses = "opacity-75 animate-pulse"; // Simple loading animation

  const inputClasses = `${baseClasses} ${className || ''} ${disabled || loading ? disabledClasses : 'bg-gray-50 border-gray-300'} ${loading ? loadingClasses : ''}`; // Added bg-gray-50

  return (
    <div>
      {label && (
        <label htmlFor={id} className="block text-gray-700 text-sm font-medium mb-2">
          {label}
        </label>
      )}
      {textarea ? (
        <textarea
          id={id}
          rows={rows}
          className={`${inputClasses} resize-y`}
          disabled={disabled || loading} // Disable when loading
          {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
        />
      ) : (
        <input
          type="text" // Default type
          id={id}
          className={inputClasses}
          disabled={disabled || loading} // Disable when loading
          {...props}
        />
      )}
    </div>
  );
};

export default Input;